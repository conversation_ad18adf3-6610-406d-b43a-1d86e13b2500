import 'package:darve/utils/server_assets.dart';
import 'package:flutter/material.dart';

class SnackbarHelper {
  static void showFollowSnackbar({
    required BuildContext context,
    required String imageUri,
    required String username,
    required Color bgColor,
    required bool isFollowed,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(50.0),
              child: Image.network(
                ServerAssets().getAssetUrl(imageUri),
                width: 40,
                height: 40,
                fit: BoxFit.cover,
              ),
            ),
            const SizedBox(width: 10),
            Expanded(
              child: Text(
                '${isFollowed ? "Followed" : "Unfollowed"} $username',
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
        backgroundColor: bgColor,
        duration: const Duration(milliseconds: 200),
      ),
    );
  }

  static void showChallengeSnackbar(
      {required BuildContext context,
      required String content,
      required Color bgColor,
      int duration = 200}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Expanded(
              child: Text(
                content,
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
        backgroundColor: bgColor,
        duration: Duration(milliseconds: duration),
      ),
    );
  }
}
