import 'package:darve/api/models/user_model.dart';
import 'package:darve/routes/route_helper.dart';
import 'package:darve/services/providers/service_provider.dart';
import 'package:darve/services/providers/auth_provider.dart';
import 'package:darve/services/auth/auth_service.dart';
import 'package:darve/api/models/follower_model.dart';
import 'package:darve/api/models/post_model.dart';
import 'package:darve/api/models/profile_data_model.dart';
import 'package:darve/utils/errors.dart';
import 'package:file_picker/file_picker.dart';
import 'package:get/get.dart';

class ProfileController extends GetxController {
  late final AuthService authService;

  // Profile data
  Rx<ProfileDataModel> userProfile = ProfileDataModel.empty().obs;
  RxBool isMe = true.obs;
  String userName;
  String userId;

  // Posts data
  RxList<PostModel> posts = <PostModel>[].obs;
  RxString content = "content".obs;

  // Followers data
  RxList<FollowerModel> followers = <FollowerModel>[].obs;

  // Loading states
  RxBool isLoadingProfile = false.obs;
  RxBool isLoadingPosts = false.obs;
  RxBool isLoadingFollowers = false.obs;
  RxBool isFollowLoading = false.obs;
  RxBool isPostCreating = false.obs;
  RxBool isChatCreating = false.obs;

  // Follow state
  RxBool isFollowing = false.obs;

  // Error handling
  final ErrorsHandle _errorHandler = ErrorsHandle();

  ProfileController({required this.userName, required this.userId});

  @override
  void onInit() {
    super.onInit();
    authService = AuthProvider.auth;
    isMe.value = user?.id == userId;
    // Auto-load profile data when controller is initialized
    loadProfileData();
  }

  @override
  void onClose() {
    // Clean up any subscriptions or resources
    super.onClose();
  }

  UserModel? get user => authService.user;

  /// Load complete profile data including profile info, posts, and followers
  Future<bool> loadProfileData() async {
    try {
      isLoadingProfile.value = true;

      // Load profile data
      final profileData = await ServiceProvider.legacyProfileRepository.getProfileData(userName);
      userProfile.value = profileData;

      // Load posts and followers concurrently
      await Future.wait([
        _loadPosts(),
        _loadFollowers(),
        if (!isMe.value) _checkFollowingStatus(),
      ]);

      return true;
    } catch (e) {
      _errorHandler.displayErrorToast(e, 'loadProfileData');
      return false;
    } finally {
      isLoadingProfile.value = false;
    }
  }

  /// Load user posts
  Future<void> _loadPosts() async {
    try {
      isLoadingPosts.value = true;
      final userPosts = await ServiceProvider.postsRepository.getPosts(userName);
      posts.value = userPosts;
    } catch (e) {
      _errorHandler.displayErrorToast(e, 'loadPosts');
    } finally {
      isLoadingPosts.value = false;
    }
  }

  /// Load followers list
  Future<void> _loadFollowers() async {
    try {
      isLoadingFollowers.value = true;
      final followersList = await ServiceProvider.legacyProfileRepository.getFollowers(userId);
      followers.value = followersList;
    } catch (e) {
      _errorHandler.displayErrorToast(e, 'loadFollowers');
    } finally {
      isLoadingFollowers.value = false;
    }
  }

  /// Check if current user is following this profile
  Future<void> _checkFollowingStatus() async {
    try {
      final followingStatus = await ServiceProvider.profileRepository.isFollowing(userId);
      isFollowing.value = followingStatus;
    } catch (e) {
      _errorHandler.displayErrorToast(e, 'checkFollowingStatus');
    }
  }

  /// Reload profile data (public method for UI)
  Future<bool> profileDataReload() async {
    return await loadProfileData();
  }

  /// Follow or unfollow user
  Future<void> toggleFollow() async {
    if (isFollowLoading.value) return;

    try {
      isFollowLoading.value = true;

      if (isFollowing.value) {
        await ServiceProvider.profileRepository.unfollowUser(userId);
        isFollowing.value = false;
      } else {
        await ServiceProvider.profileRepository.followUser(userId);
        isFollowing.value = true;
      }

      // Reload profile data to update follower counts
      await loadProfileData();

    } catch (e) {
      _errorHandler.displayErrorToast(e, 'toggleFollow');
    } finally {
      isFollowLoading.value = false;
    }
  }

  /// Create a new post
  Future<void> createPost({String? filePath}) async {
    if (isPostCreating.value) return;

    try {
      isPostCreating.value = true;

      String? selectedFilePath = filePath;

      // If no file path provided, let user pick one
      if (selectedFilePath == null) {
        final result = await FilePicker.platform.pickFiles(
          type: FileType.image,
        );

        if (result != null && result.files.single.path != null) {
          selectedFilePath = result.files.single.path!;
        } else {
          return; // User cancelled file selection
        }
      }

      // Create the post
      await ServiceProvider.postsRepository.createPost(
        content.value,
        filePath: selectedFilePath,
      );

      // Reload posts to show the new post
      await _loadPosts();

    } catch (e) {
      _errorHandler.displayErrorToast(e, 'createPost');
    } finally {
      isPostCreating.value = false;
    }
  }

  /// Create chat with this user
  Future<String?> createChatWithUser() async {
    if (isChatCreating.value) return null;

    try {
      isChatCreating.value = true;

      final chatData = await ServiceProvider.chatRepository.createChatWithUserId(userId);

      if (chatData != null && chatData['discussion_id'] != null) {
        final discussionId = chatData['discussion_id'] as String;
        RouteHelper.goToChat(
          chatId: discussionId,
          title: userName,
          avatarUrl: userProfile.value.imageUri ?? '',
          userId: userId,
        );
        return chatData['discussion_id'] as String;
      }

      return null;
    } catch (e) {
      _errorHandler.displayErrorToast(e, 'createChatWithUser');
      return null;
    } finally {
      isChatCreating.value = false;
    }
  }

  /// Legacy method for backward compatibility
  Future<void> makePost(String? pickedFilePath) async {
    await createPost(filePath: pickedFilePath);
  }
}
