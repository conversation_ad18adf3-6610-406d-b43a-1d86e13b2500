import 'package:darve/ui/profile/insights/profile_insights_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../components/profile/profile_card.dart';

class ProfileInsightsPage extends GetView<ProfileInsightsController> {
  final String userId;
  final int initialTab;

  const ProfileInsightsPage({
    super.key,
    required this.userId,
    this.initialTab = 0,
  });

  @override
  String? get tag => 'profile_insights_$userId';

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      initialIndex: initialTab,
      length: 2,
      child: Scaffold(
        appBar: AppBar(
          title: const Text(
            'Profile Insights',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.black,
              fontSize: 22,
            ),
          ),
          backgroundColor: Colors.white,
          elevation: 0,
          bottom: const TabBar(
            indicatorSize: TabBarIndicatorSize.tab,
            labelColor: Colors.black,
            unselectedLabelColor: Colors.grey,
            tabs: [
              Tab(text: 'Followers'),
              Tab(text: 'Following'),
            ],
          ),
        ),
        body: Container(
          color: Colors.white,
          child: Obx(() => TabBarView(
            children: [
              // Followers Tab
              controller.hasNoFollowers
                  ? const Center(
                      child: Text(
                      "No Followers yet",
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ))
                  : controller.isLoadingFollowers.value
                      ? const Center(child: CircularProgressIndicator())
                      : GridView.builder(
                          padding: const EdgeInsets.all(16),
                          gridDelegate:
                              const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 2,
                            childAspectRatio: 1,
                            crossAxisSpacing: 16,
                            mainAxisSpacing: 16,
                          ),
                          itemCount: controller.followers.length,
                          itemBuilder: (context, index) {
                            final follower = controller.followers[index];
                            return ProfileCard(
                              userName: follower.username,
                              subText: 'Followed by Alex and ${index + 3} others',
                              imageUrl: follower.imageUrl,
                              btnVal: 'Remove',
                              onBtnClick: () {
                                // TODO: Implement remove follower functionality
                              },
                            );
                          },
                        ),

              // Following Tab
              controller.hasNoFollowing
                  ? const Center(
                      child: Text(
                      "User not following anyone.",
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ))
                  : controller.isLoadingFollowing.value
                      ? const Center(child: CircularProgressIndicator())
                      : GridView.builder(
                          padding: const EdgeInsets.all(16),
                          gridDelegate:
                              const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 2,
                            childAspectRatio: 1,
                            crossAxisSpacing: 16,
                            mainAxisSpacing: 16,
                          ),
                          itemCount: controller.following.length,
                          itemBuilder: (context, index) {
                            final following = controller.following[index];
                            return ProfileCard(
                              userName: following.username,
                              subText: 'Followed by Sarah and ${index + 2} others',
                              imageUrl: following.imageUrl,
                              btnVal: 'Following',
                              onBtnClick: () async {
                                await controller.unfollowUser(following.username, context);
                              },
                            );
                          },
                        ),
            ],
          )),
        ),
      ),
    );
  }
}
