import 'package:darve/api/models/follower_model.dart';
import 'package:darve/services/providers/service_provider.dart';
import 'package:darve/ui/core/entity_state_widget.dart';
import 'package:darve/utils/errors.dart';
import 'package:darve/utils/show_snackbar.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ProfileInsightsController extends GetxController {
  // Required parameters
  final String userId;
  final int initialTab;

  // Entity state management using ViewModel
  final Rx<ViewModel<List<FollowerModel>>> followersState = const ViewModel<List<FollowerModel>>.loading().obs;
  final Rx<ViewModel<List<FollowerModel>>> followingState = const ViewModel<List<FollowerModel>>.loading().obs;

  // Error handling
  final ErrorsHandle _errorHandler = ErrorsHandle();

  ProfileInsightsController({
    required this.userId,
    this.initialTab = 0,
  });

  @override
  void onInit() {
    super.onInit();
    // Auto-load data when controller is initialized
    loadInsightsData();
  }

  @override
  void onClose() {
    // Clean up any subscriptions or resources
    super.onClose();
  }

  /// Load both followers and following data
  Future<void> loadInsightsData() async {
    // Load both followers and following concurrently
    await Future.wait([
      loadFollowers(),
      loadFollowing(),
    ]);
  }

  /// Load followers list
  Future<void> loadFollowers() async {
    followersState.value = const ViewModel<List<FollowerModel>>.loading();

    final result =
        await ServiceProvider.profileRepositoryNew.getFollowersResult(userId);

    followersState.value = result.fold(
      (error) {
        // Handle error case
        _errorHandler.displayErrorToast(error, 'loadFollowers');
        return ViewModel<List<FollowerModel>>.error(error);
      },
      (followersList) {
        // Handle success case
        if (followersList.isEmpty) {
          return const ViewModel<List<FollowerModel>>.empty();
        } else {
          return ViewModel<List<FollowerModel>>.content(followersList);
        }
      },
    );
  }

  /// Load following list
  Future<void> loadFollowing() async {
    followingState.value = const ViewModel<List<FollowerModel>>.loading();

    final result = await ServiceProvider.profileRepositoryNew.getFollowingResult(userId);

    result.fold(
      (error) {
        // Handle error case
        followingState.value = ViewModel<List<FollowerModel>>.error(error);
        _errorHandler.displayErrorToast(error, 'loadFollowing');
      },
      (followingList) {
        // Handle success case
        if (followingList.isEmpty) {
          followingState.value = const ViewModel<List<FollowerModel>>.empty();
        } else {
          followingState.value = ViewModel<List<FollowerModel>>.content(followingList);
        }
      },
    );
  }

  /// Refresh all data (public method for UI)
  Future<void> refreshInsightsData() async {
    await loadInsightsData();
  }

  /// Unfollow a user from the following list
  Future<void> unfollowUser(String username, BuildContext context) async {
    // Get user details first
    final profileResult = await ServiceProvider.profileRepositoryNew.getProfileDataResult(username);

    profileResult.fold(
      (error) {
        // Handle profile fetch error
        _errorHandler.displayErrorToast(error, 'unfollowUser');
      },
      (details) async {
        if (details.userId != null) {
          // Unfollow the user
          final unfollowResult = await ServiceProvider.profileRepositoryNew.unfollowUserResult(details.userId!);

          unfollowResult.fold(
            (error) {
              // Handle unfollow error
              _errorHandler.displayErrorToast(error, 'unfollowUser');
            },
            (success) async {
              // Refresh the data to update the lists
              await refreshInsightsData();

              // Show success feedback - check if context is still mounted
              if (context.mounted) {
                SnackbarHelper.showFollowSnackbar(
                  context: context,
                  imageUri: details.imageUri ?? '',
                  username: details.userName ?? username,
                  bgColor: Colors.red,
                  isFollowed: false,
                );
              }
            },
          );
        }
      },
    );
  }



  /// Get followers data for direct access (if needed)
  List<FollowerModel>? get followers => followersState.value.data;

  /// Get following data for direct access (if needed)
  List<FollowerModel>? get following => followingState.value.data;
}
