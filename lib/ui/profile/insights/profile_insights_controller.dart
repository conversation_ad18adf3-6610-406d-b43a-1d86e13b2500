import 'package:darve/api/models/follower_model.dart';
import 'package:darve/services/providers/service_provider.dart';
import 'package:darve/ui/core/entity_state_widget.dart';
import 'package:darve/utils/errors.dart';
import 'package:darve/utils/result.dart';
import 'package:darve/utils/show_snackbar.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 🎯 PERFECT EXAMPLE: Modern State Management Controller
///
/// This controller demonstrates the STANDARD PATTERN for all data-loading screens.
///
/// ✅ What this example teaches:
/// - How to use ViewModel<T> for state management
/// - How to convert Result types to ViewModel states
/// - How to handle errors functionally (no try-catch blocks)
/// - How to structure reactive controllers with GetX
///
/// 📚 NEWCOMERS: Copy this pattern for any new controller that loads data!
/// 📁 Related files to study:
/// - lib/ui/core/entity_state_widget.dart (for UI display)
/// - lib/utils/result.dart (for Result extension methods)
/// - lib/api/repositories/profile_repository.dart (for Result-based API calls)
class ProfileInsightsController extends GetxController {
  // Required parameters
  final String userId;
  final int initialTab;

  // ========== STATE VARIABLES ==========
  // 📝 NEWCOMER TIP: Every piece of data that can be loading/empty/error needs a ViewModel
  // 📝 PATTERN: ViewModel<YourDataType> + .obs for GetX reactivity

  /// Followers list state - demonstrates list data management
  /// 🔍 Possible states: loading → content/empty/error
  /// 🎯 Copy this pattern for any list data in your controllers
  final Rx<ViewModel<List<FollowerModel>>> followersState = const ViewModel<List<FollowerModel>>.loading().obs;

  /// Following list state - same pattern, different data
  /// 🔍 Notice: Same ViewModel pattern works for any data type
  final Rx<ViewModel<List<FollowerModel>>> followingState = const ViewModel<List<FollowerModel>>.loading().obs;

  // ========== DEPENDENCIES ==========
  // 📝 PATTERN: Use dependency injection, not direct instantiation
  // 📝 TIP: ErrorsHandle manages all error display logic
  final ErrorsHandle _errorHandler = ErrorsHandle();

  ProfileInsightsController({
    required this.userId,
    this.initialTab = 0,
  });

  // ========== LIFECYCLE METHODS ==========

  @override
  void onInit() {
    super.onInit();
    // 📝 PATTERN: Auto-load data when controller is initialized
    // 🎯 This ensures data is ready when the UI appears
    loadInsightsData();
  }

  @override
  void onClose() {
    // 📝 PATTERN: Clean up any subscriptions or resources
    // 🎯 GetX automatically disposes reactive variables, but add custom cleanup here
    super.onClose();
  }

  // ========== PUBLIC METHODS ==========

  /// Load both followers and following data concurrently
  /// 🎯 PATTERN: Use Future.wait for parallel loading when data is independent
  /// 📝 TIP: This is called from onInit() and can be called for refresh
  Future<void> loadInsightsData() async {
    // Load both followers and following concurrently
    await Future.wait([
      loadFollowers(),
      loadFollowing(),
    ]);
  }

  /// 🎯 PERFECT EXAMPLE: Modern Data Loading Pattern
  ///
  /// This method demonstrates the STANDARD APPROACH for loading list data.
  ///
  /// ✅ What this teaches:
  /// 1. Set loading state first
  /// 2. Call Result-based repository method
  /// 3. Use extension method to convert Result → ViewModel
  /// 4. Handle errors with onError callback
  ///
  /// 📚 COPY THIS PATTERN for any list data loading!
  Future<void> loadFollowers() async {
    // 📝 STEP 1: Always set loading state first
    // 🎯 This shows spinner/skeleton in UI immediately
    followersState.value = const ViewModel<List<FollowerModel>>.loading();

    // 📝 STEP 2: Call repository method that returns Result<T>
    // 🎯 Notice: No try-catch needed! Result handles errors internally
    final result = await ServiceProvider.profileRepositoryNew.getFollowersResult(userId);

    // 📝 STEP 3: Convert Result to ViewModel using extension method
    // 🎯 toListViewModel() automatically handles: content/empty/error states
    // 🎯 onError callback: Shows toast and reports error (side effect)
    followersState.value = result.toListViewModel<FollowerModel>(
      onError: (error) => _errorHandler.displayErrorToast(error, 'loadFollowers'),
    );
  }

  /// 📚 SAME PATTERN: Following list loading
  ///
  /// 🎯 Notice: Identical pattern to loadFollowers() - this is the power of consistency!
  /// 📝 Copy this exact structure for any similar data loading
  Future<void> loadFollowing() async {
    // Same 3-step pattern as loadFollowers()
    followingState.value = const ViewModel<List<FollowerModel>>.loading();
    final result = await ServiceProvider.profileRepositoryNew.getFollowingResult(userId);
    followingState.value = result.toListViewModel<FollowerModel>(
      onError: (error) => _errorHandler.displayErrorToast(error, 'loadFollowing'),
    );
  }

  /// 🔄 REFRESH PATTERN: Reload data without showing loading state
  ///
  /// 🎯 Use this for pull-to-refresh or retry functionality
  /// 📝 TIP: Calls the same load methods, UI handles loading states
  Future<void> refreshInsightsData() async {
    await loadInsightsData();
  }

  /// Unfollow a user from the following list
  Future<void> unfollowUser(String username, BuildContext context) async {
    // Get user details first
    final profileResult = await ServiceProvider.profileRepositoryNew.getProfileDataResult(username);

    await profileResult.fold(
      (error) async {
        // Handle profile fetch error
        _errorHandler.displayErrorToast(error, 'unfollowUser');
      },
      (details) async {
        if (details.userId != null) {
          // Unfollow the user
          final unfollowResult = await ServiceProvider.profileRepositoryNew.unfollowUserResult(details.userId!);

          await unfollowResult.fold(
            (error) async {
              // Handle unfollow error
              _errorHandler.displayErrorToast(error, 'unfollowUser');
            },
            (success) async {
              // Refresh the data to update the lists
              await refreshInsightsData();

              // Show success feedback - check if context is still mounted
              if (context.mounted) {
                SnackbarHelper.showFollowSnackbar(
                  context: context,
                  imageUri: details.imageUri ?? '',
                  username: details.userName ?? username,
                  bgColor: Colors.red,
                  isFollowed: false,
                );
              }
            },
          );
        }
      },
    );
  }



  /// Get followers data for direct access (if needed)
  List<FollowerModel>? get followers => followersState.value.data;

  /// Get following data for direct access (if needed)
  List<FollowerModel>? get following => followingState.value.data;
}
