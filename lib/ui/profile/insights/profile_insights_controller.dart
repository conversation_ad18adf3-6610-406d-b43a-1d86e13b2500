import 'package:darve/api/models/follower_model.dart';
import 'package:darve/services/providers/service_provider.dart';
import 'package:darve/ui/core/entity_state_widget.dart';
import 'package:darve/utils/errors.dart';
import 'package:darve/utils/show_snackbar.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ProfileInsightsController extends GetxController {
  // Required parameters
  final String userId;
  final int initialTab;

  // Entity state management using ViewModel
  final Rx<ViewModel<List<FollowerModel>>> followersState = const ViewModel<List<FollowerModel>>.loading().obs;
  final Rx<ViewModel<List<FollowerModel>>> followingState = const ViewModel<List<FollowerModel>>.loading().obs;

  // Error handling
  final ErrorsHandle _errorHandler = ErrorsHandle();

  ProfileInsightsController({
    required this.userId,
    this.initialTab = 0,
  });

  @override
  void onInit() {
    super.onInit();
    // Auto-load data when controller is initialized
    loadInsightsData();
  }

  @override
  void onClose() {
    // Clean up any subscriptions or resources
    super.onClose();
  }

  /// Load both followers and following data
  Future<void> loadInsightsData() async {
    // Load both followers and following concurrently
    await Future.wait([
      loadFollowers(),
      loadFollowing(),
    ]);
  }

  /// Load followers list
  Future<void> loadFollowers() async {
    try {
      followersState.value = const ViewModel<List<FollowerModel>>.loading();
      final followersList = await ServiceProvider.profileRepository.getFollowers(userId);

      if (followersList.isEmpty) {
        followersState.value = const ViewModel<List<FollowerModel>>.empty();
      } else {
        followersState.value = ViewModel<List<FollowerModel>>.content(followersList);
      }
    } catch (e) {
      final errorMessage = _getErrorMessage(e);
      followersState.value = ViewModel<List<FollowerModel>>.error(errorMessage);
      _errorHandler.displayErrorToast(e, 'loadFollowers');
    }
  }

  /// Load following list
  Future<void> loadFollowing() async {
    try {
      followingState.value = const ViewModel<List<FollowerModel>>.loading();
      final followingList = await ServiceProvider.profileRepository.getFollowing(userId);

      if (followingList.isEmpty) {
        followingState.value = const ViewModel<List<FollowerModel>>.empty();
      } else {
        followingState.value = ViewModel<List<FollowerModel>>.content(followingList);
      }
    } catch (e) {
      final errorMessage = _getErrorMessage(e);
      followingState.value = ViewModel<List<FollowerModel>>.error(errorMessage);
      _errorHandler.displayErrorToast(e, 'loadFollowing');
    }
  }

  /// Refresh all data (public method for UI)
  Future<void> refreshInsightsData() async {
    await loadInsightsData();
  }

  /// Unfollow a user from the following list
  Future<void> unfollowUser(String username, BuildContext context) async {
    try {
      // Get user details first
      final details = await ServiceProvider.profileRepository.getProfileData(username);

      if (details.userId != null) {
        // Unfollow the user
        await ServiceProvider.profileRepository.unfollowUser(details.userId!);

        // Refresh the data to update the lists
        await refreshInsightsData();

        // Show success feedback - check if context is still mounted
        if (context.mounted) {
          SnackbarHelper.showFollowSnackbar(
            context: context,
            imageUri: details.imageUri ?? '',
            username: details.userName ?? username,
            bgColor: Colors.red,
            isFollowed: false,
          );
        }
      }
    } catch (e) {
      _errorHandler.displayErrorToast(e, 'unfollowUser');
    }
  }

  /// Extract user-friendly error message from exception
  String _getErrorMessage(dynamic error) {
    if (error.toString().contains('404')) {
      return 'User not found';
    } else if (error.toString().contains('403')) {
      return 'Access denied';
    } else if (error.toString().contains('timeout')) {
      return 'Connection timeout. Please check your internet connection.';
    } else if (error.toString().contains('network')) {
      return 'Network error. Please check your connection.';
    } else {
      return 'Something went wrong. Please try again.';
    }
  }

  /// Get followers data for direct access (if needed)
  List<FollowerModel>? get followers => followersState.value.data;

  /// Get following data for direct access (if needed)
  List<FollowerModel>? get following => followingState.value.data;
}
