import 'package:darve/api/models/follower_model.dart';
import 'package:darve/services/providers/service_provider.dart';
import 'package:darve/utils/errors.dart';
import 'package:darve/utils/show_snackbar.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ProfileInsightsController extends GetxController {
  // Required parameters
  final String userId;
  final int initialTab;

  // Data state
  RxList<FollowerModel> following = <FollowerModel>[].obs;
  RxList<FollowerModel> followers = <FollowerModel>[].obs;

  // Loading states
  RxBool isLoadingFollowing = false.obs;
  RxBool isLoadingFollowers = false.obs;
  RxBool isLoadingData = false.obs;

  // Error handling
  final ErrorsHandle _errorHandler = ErrorsHandle();

  ProfileInsightsController({
    required this.userId,
    this.initialTab = 0,
  });

  @override
  void onInit() {
    super.onInit();
    // Auto-load data when controller is initialized
    loadInsightsData();
  }

  @override
  void onClose() {
    // Clean up any subscriptions or resources
    super.onClose();
  }

  /// Load both followers and following data
  Future<void> loadInsightsData() async {
    try {
      isLoadingData.value = true;
      
      // Load both followers and following concurrently
      await Future.wait([
        _loadFollowers(),
        _loadFollowing(),
      ]);
      
    } catch (e) {
      _errorHandler.displayErrorToast(e, 'loadInsightsData');
    } finally {
      isLoadingData.value = false;
    }
  }

  /// Load followers list
  Future<void> _loadFollowers() async {
    try {
      isLoadingFollowers.value = true;
      final followersList = await ServiceProvider.profileRepository.getFollowers(userId);
      followers.value = followersList;
    } catch (e) {
      _errorHandler.displayErrorToast(e, 'loadFollowers');
    } finally {
      isLoadingFollowers.value = false;
    }
  }

  /// Load following list
  Future<void> _loadFollowing() async {
    try {
      isLoadingFollowing.value = true;
      final followingList = await ServiceProvider.profileRepository.getFollowing(userId);
      following.value = followingList;
    } catch (e) {
      _errorHandler.displayErrorToast(e, 'loadFollowing');
    } finally {
      isLoadingFollowing.value = false;
    }
  }

  /// Refresh all data (public method for UI)
  Future<void> refreshInsightsData() async {
    await loadInsightsData();
  }

  /// Unfollow a user from the following list
  Future<void> unfollowUser(String username, BuildContext context) async {
    try {
      // Get user details first
      final details = await ServiceProvider.profileRepository.getProfileData(username);
      
      if (details.userId != null) {
        // Unfollow the user
        await ServiceProvider.profileRepository.unfollowUser(details.userId!);
        
        // Refresh the data to update the lists
        await refreshInsightsData();
        
        // Show success feedback
        SnackbarHelper.showFollowSnackbar(
          context: context,
          imageUri: details.imageUri ?? '',
          username: details.userName ?? username,
          bgColor: Colors.red,
          isFollowed: false,
        );
      }
    } catch (e) {
      _errorHandler.displayErrorToast(e, 'unfollowUser');
    }
  }

  /// Get loading state for UI
  bool get isLoading => isLoadingData.value || isLoadingFollowers.value || isLoadingFollowing.value;

  /// Check if followers list is empty
  bool get hasNoFollowers => followers.isEmpty && !isLoadingFollowers.value;

  /// Check if following list is empty
  bool get hasNoFollowing => following.isEmpty && !isLoadingFollowing.value;
}
