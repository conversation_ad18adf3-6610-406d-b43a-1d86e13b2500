import 'package:darve/ui/components/reels/challenge_tile.dart';
import 'package:darve/ui/components/reels/comment_tile.dart';
import 'package:darve/ui/components/reels/reels_icon.dart';
import 'package:darve/ui/components/reels/toggle_comments.dart';
import 'package:darve/ui/components/common/reel_child.dart';
import 'package:darve/routes/route_helper.dart';
import 'package:darve/services/providers/service_provider.dart';
import 'package:darve/services/providers/auth_provider.dart';
import 'package:darve/services/auth/auth_service.dart';
import 'package:darve/api/models/post_model.dart';
import 'package:darve/api/models/shortened_challenge_model.dart';
import 'package:darve/api/models/shortened_comment_model.dart';
import 'package:darve/utils/participants.dart';
import 'package:darve/utils/server_assets.dart';
import 'package:flutter/material.dart';
import 'package:darve/utils/styles.dart';

class PersonalReelView extends StatefulWidget {
  final PostModel post;
  const PersonalReelView(this.post, {super.key});

  @override
  State<PersonalReelView> createState() => _PersonalReelViewState();
}

class _PersonalReelViewState extends State<PersonalReelView> {
  bool isChallengeSelected = false;
  List<ShortenedChallengeModel> challenges = [];
  List<ShortenedCommentModel> comments = [];
  bool showComments = false;

  late final AuthService authService;

  Future<void> refreshComments() async {
    var val = await ServiceProvider.postsRepository.getPostDiscussions(widget.post.belongsToId, widget.post.id);
    setState(() {
      comments = val;
    });
  }

  @override
  void initState() {
    super.initState();
    authService = AuthProvider.auth;
    refreshComments();
    loadChallenges();
  }

  Future<List<ShortenedChallengeModel>> loadChallenges() async {
    var _challenges = await ServiceProvider.challengeRepository.getGivenTaskRequestsForPost(widget.post.id);
    List<ShortenedChallengeModel> parsedChallenges = [];

    for (var i = 0; i < _challenges.length; i++) {
      var challengeUserDetails =
          await ServiceProvider.legacyProfileRepository.getProfileData(_challenges[i].fromUserId);

      parsedChallenges.add(ShortenedChallengeModel.fromDetails(
          _challenges[i].id,
          challengeUserDetails.imageUri,
          challengeUserDetails.userName,
          _challenges[i].requestText,
          cumulateBalance(_challenges[i].participants),
          _challenges[i].participants));
    }

    setState(() {
      challenges = parsedChallenges;
    });
    return parsedChallenges;
  }

  void onChallengeChange(bool _isChallengeSelected) async {
    setState(() {
      isChallengeSelected = _isChallengeSelected;
    });
  }

  void addChallenge(
      String value, Function modalSetState, double challengeAmount) async {
    var userDetails =
        await ServiceProvider.profileRepository.getProfileData(widget.post.username);
    var toUserId = userDetails.userId;

    //checking if content includes username
    bool containsUsername = value.contains("@");
    if (containsUsername) {
      var usernameToBeChallenged = value.split("@")[1].split(" ")[0];
      userDetails =
          await ServiceProvider.profileRepository.getProfileData(usernameToBeChallenged);
      if (userDetails.userId != null) {
        toUserId = userDetails.userId;
      }
    }

    await ServiceProvider.challengeRepository.createTaskRequest(
      toUserId: toUserId!,
      content: value,
      postId: widget.post.id,
      offerAmount: challengeAmount,
    );

    var updatedChallenges = await ServiceProvider.challengeRepository.getGivenTaskRequestsForPost(widget.post.id);
    List<ShortenedChallengeModel> parsedChallenges = [];

    for (var challenge in updatedChallenges) {
      debugPrint("challenge===$challenge");
      var challengeUserDetails =
          await ServiceProvider.legacyProfileRepository.getProfileData(challenge.fromUserId);
      parsedChallenges.add(ShortenedChallengeModel(
          id: challenge.id,
          avatar: challengeUserDetails.imageUri,
          username: challengeUserDetails.userName,
          challenge: challenge.requestText,
          participants: challenge.participants,
          bet: challenge.participants.isNotEmpty
              ? challenge.participants[0].amount.toString()
              : "0"));
    }

    modalSetState(() {
      challenges = parsedChallenges;
    });
  }

  void handlePostChallenge(String value, Function modalSetState) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        bool showCustomField = false;
        TextEditingController customController = TextEditingController();

        return AlertDialog(
          backgroundColor: Colors.white,
          title: const Text(
            'Select Challenge Amount',
            style: TextStyle(fontWeight: FontWeight.w600, fontSize: 18),
          ),
          content: StatefulBuilder(
            builder: (context, setState) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (!showCustomField) ...[
                    _buildButton(context, "\$10.0", () {
                      addChallenge(value, modalSetState, 10.0);
                      Navigator.pop(context);
                    }),
                    _buildButton(context, "\$20.0", () {
                      addChallenge(value, modalSetState, 20.0);
                      Navigator.pop(context);
                    }),
                    _buildButton(context, "\$30.0", () {
                      addChallenge(value, modalSetState, 30.0);
                      Navigator.pop(context);
                    }),
                    _buildButton(context, "Custom", () {
                      setState(() {
                        showCustomField = true;
                      });
                    }),
                  ],
                  if (showCustomField) ...[
                    TextField(
                      controller: customController,
                      keyboardType: TextInputType.number,
                      decoration: const InputDecoration(
                        hintText: 'Enter custom amount',
                        border: OutlineInputBorder(),
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                      ),
                    ),
                    const SizedBox(height: 10),
                    ElevatedButton(
                      onPressed: () {
                        double? customAmount =
                            double.tryParse(customController.text);
                        if (customAmount != null && customAmount > 0) {
                          addChallenge(value, modalSetState, customAmount);
                          Navigator.pop(context);
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Styles.primaryColor,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 50, vertical: 6),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(30),
                        ),
                      ),
                      child: const Text(
                        "Submit",
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ],
                ],
              );
            },
          ),
        );
      },
    );
  }

  void _showCommentModal() {
    TextEditingController commentController = TextEditingController();

    void handleComment(String value, Function modalSetState) async {
      if (value.trim().isNotEmpty) {
        if (isChallengeSelected) {
          handlePostChallenge(value, modalSetState);
        } else {
          var commentResponse = await ServiceProvider.postsRepository.postInDiscussion(
            widget.post.belongsToId,
            widget.post.postTitleUri,
            value,
          );

          if (commentResponse) {
            modalSetState(() {
              comments.insert(
                  0,
                  ShortenedCommentModel(
                      avatar: authService.user?.imageUri,
                      username: authService.user?.username,
                      comment: value,
                      likes: "0"));
            });
          } else {
            Navigator.pop(context);
          }
        }
        commentController.clear();
      }
    }

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (BuildContext modalContext, modalSetState) {
            return Padding(
              padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom,
              ),
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(30),
                ),
                padding: const EdgeInsets.all(16.0),
                height: MediaQuery.of(context).size.height * 0.6,
                child: Column(
                  children: [
                    ToggleComments(
                      isChallengeSelected: isChallengeSelected,
                      onChange: (value) {
                        modalSetState(() {
                          isChallengeSelected = value;
                        });
                      },
                    ),
                    Expanded(
                      child: isChallengeSelected
                          ? challenges.isEmpty
                              ? Column(
                                  children: [
                                    Divider(
                                      color: Colors.grey[100],
                                    ),
                                    const SizedBox(height: 8.0),
                                    const Text(
                                      "No Challenges yet!",
                                      style: Styles.lightTextStyle,
                                    ),
                                  ],
                                )
                              : ListView.builder(
                                  itemCount: challenges.length,
                                  itemBuilder: (context, index) {
                                    return ChallengeTile(
                                        key: ValueKey(
                                            "${challenges[index].id}-${challenges[index].participants!.length}"),
                                        challenges[index],
                                        loadChallenges);
                                  },
                                )
                          : comments.isEmpty
                              ? Column(
                                  children: [
                                    Divider(
                                      color: Colors.grey[100],
                                    ),
                                    const SizedBox(height: 8.0),
                                    const Text(
                                      "No Comments yet!",
                                      style: Styles.lightTextStyle,
                                    ),
                                  ],
                                )
                              : ListView.separated(
                                  itemCount: comments.length,
                                  itemBuilder: (context, index) {
                                    return CommentTile(comments[index]);
                                  },
                                  separatorBuilder: (context, index) =>
                                      const Padding(
                                    padding:
                                        EdgeInsets.symmetric(horizontal: 16.0),
                                    child: Divider(
                                      color: Colors.grey,
                                      height: 1.0,
                                    ),
                                  ),
                                ),
                    ),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Container(
                          width: 52,
                          height: 52,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: Styles.primaryColor,
                              width: 2.0,
                            ),
                          ),
                          child: CircleAvatar(
                            radius: 24,
                            backgroundImage: NetworkImage(ServerAssets()
                                .getAssetUrl(authService.user?.imageUri ?? '')),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: TextField(
                            controller: commentController,
                            onSubmitted: (value) {
                              handleComment(value, modalSetState);
                            },
                            decoration: InputDecoration(
                              hintText:
                                  isChallengeSelected ? 'Challenge' : 'Comment',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(25),
                              ),
                              filled: true,
                              fillColor: Colors.white,
                              contentPadding: const EdgeInsets.symmetric(
                                horizontal: 15,
                                vertical: 4,
                              ),
                            ),
                          ),
                        ),
                        Container(
                          decoration: const BoxDecoration(
                            color: Styles.primaryColor,
                            shape: BoxShape.circle,
                          ),
                          child: IconButton(
                            icon: const Icon(Icons.send, color: Colors.white),
                            onPressed: () {
                              handleComment(
                                  commentController.text, modalSetState);
                            },
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: null,
      body: GestureDetector(
        onTap: () {
          if (showComments) {
            setState(() {
              showComments = false;
            });
          }
        },
        child: Stack(
          children: [
            // Full-screen background image
            ReelChild(widget.post),
            Positioned.fill(
              child: Align(
                alignment: Alignment.topCenter,
                child: Container(
                  width: 400,
                  height: 250,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.black.withValues(alpha: .5),
                        Colors.transparent,
                      ],
                    ),
                  ),
                ),
              ),
            ),

            Positioned.fill(
              child: Align(
                alignment: Alignment.centerRight,
                child: Container(
                  width: 150,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                      colors: [
                        Colors.transparent,
                        Colors.black.withValues(alpha: .4),
                      ],
                    ),
                  ),
                ),
              ),
            ),

            // Back button at the top left
            Positioned(
              top: 40,
              left: 10,
              child: IconButton(
                icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
                onPressed: () {
                  Navigator.pop(context); // Go back to the previous screen
                },
              ),
            ),

            // Title "Post Details" at the top center
            const Positioned(
              top: 50,
              left: 0,
              right: 0,
              child: Center(
                child: Text(
                  'Post Details',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),
              ),
            ),

            // Content (caption) at the bottom with transparent background
            Positioned(
              bottom: 30,
              left: 16,
              right: 16,
              child: FutureBuilder(
                future: ServiceProvider.profileRepository
                    .getProfileData(widget.post.username), // Fetch profile data
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const CircularProgressIndicator();
                  }
                  if (snapshot.hasError) {
                    return const Center(
                        child: Text('Error loading profile data'));
                  }
                  if (!snapshot.hasData) {
                    return const Center(
                        child: Text('No profile data available'));
                  }

                  var profileData = snapshot.data;

                  return Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.black.withValues(alpha: .8),
                          Colors.black,
                        ],
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        GestureDetector(
                          onTap: () {
                            // Use proper route navigation
                            RouteHelper.goToProfile(
                              userId: profileData.userId!,
                              username: profileData.userName!,
                              imageUrl: profileData.imageUri!,
                              fullName: profileData.fullName!,
                            );
                          },
                          child: CircleAvatar(
                            radius: 24,
                            backgroundImage: NetworkImage(ServerAssets()
                                .getAssetUrl(profileData!.imageUri!)),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                profileData.fullName!,
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                  fontSize: 16,
                                ),
                              ),
                              Text(
                                '@${profileData.userName}',
                                style: TextStyle(
                                  fontWeight: FontWeight.w400,
                                  color: Styles.textLightColor
                                      .withValues(alpha: .7),
                                  fontSize: 14,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                widget.post.content,
                                style: const TextStyle(
                                  fontWeight: FontWeight.w600,
                                  color: Styles.textLightColor,
                                  fontSize: 16,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),

            // Positioned action buttons (like, comment, share, etc.)
            Positioned(
              bottom: 80,
              right: 10,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  //Todo replace with likes
                  ReelsIcon('assets/images/home/<USER>', () {}, "0"),
                  const SizedBox(height: 20),
                  ReelsIcon('assets/images/home/<USER>', _showCommentModal,
                      comments.length.toString()),
                  const SizedBox(height: 20),
                  //Todo replace with shares counts
                  ReelsIcon('assets/images/home/<USER>', () {}, "0"),
                  const SizedBox(height: 20),
                  ReelsIcon('assets/images/home/<USER>', () {}, "More"),
                ],
              ),
            ),

            // Show comment section when triggered
            // if (showComments)
            // Positioned(
            //   bottom: 0,
            //   left: 0,
            //   right: 0,
            //   child: GestureDetector(
            //     onTap: () {
            //       // Prevent closing when tapping inside the comment section
            //     },
            //     child: Container(
            //       height:
            //           300, // Adjust based on the desired height for the comments section
            //       color: Colors.white,
            //       child: Column(
            //         children: [
            //           // Add comment input and existing comments here
            //           Padding(
            //             padding: const EdgeInsets.all(8.0),
            //             child: TextField(
            //               decoration: InputDecoration(
            //                 hintText: 'Write a comment...',
            //                 border: OutlineInputBorder(),
            //                 contentPadding: EdgeInsets.symmetric(
            //                     horizontal: 16, vertical: 10),
            //               ),
            //             ),
            //           ),
            //           Expanded(
            //             child: ListView.builder(
            //               itemCount:
            //                   5, // Number of comments, replace with actual data
            //               itemBuilder: (context, index) {
            //                 return ListTile(
            //                   leading: CircleAvatar(
            //                     backgroundImage: NetworkImage(
            //                         'https://randomuser.me/api/portraits/men/1.jpg'),
            //                   ),
            //                   title: Text('User $index'),
            //                   subtitle: Text('Great post!'),
            //                 );
            //               },
            //             ),
            //           ),
            //         ],
            //       ),
            //     ),
            //   ),
            // ),
          ],
        ),
      ),
    );
  }
}

Widget _buildButton(
    BuildContext context, String title, VoidCallback handleClick) {
  return Container(
    margin: const EdgeInsets.all(4),
    child: TextButton(
      onPressed: handleClick,
      style: TextButton.styleFrom(
        minimumSize: const Size(double.infinity, 40),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        backgroundColor: Colors.grey[200],
      ),
      child: Row(
        children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Text(
              title,
              style: const TextStyle(fontSize: 16, color: Colors.black),
            ),
          ),
        ],
      ),
    ),
  );
}
