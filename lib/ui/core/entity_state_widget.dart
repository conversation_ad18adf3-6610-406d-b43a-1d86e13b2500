import 'package:flutter/material.dart';
import 'package:get/get.dart';

typedef WidgetCallback<T> = Widget Function(T data);
typedef EmptyWidgetBuilder = Widget Function(String? message);
typedef ErrorWidgetBuilder = Widget Function(String? message, VoidCallback? onRetry);
typedef LoadingWidgetBuilder = Widget Function();

class EntityStateWidget<T> extends StatefulWidget {
  final Rx<ViewModel<T>> model;
  final WidgetCallback<T>? itemBuilder;
  final LoadingWidgetBuilder? loadingBuilder;
  final EmptyWidgetBuilder? emptyBuilder;
  final ErrorWidgetBuilder? errorBuilder;
  final VoidCallback? onRetry;
  final String? emptyMessage;
  final Widget? emptyIcon;

  const EntityStateWidget({
    super.key,
    required this.model,
    this.itemBuilder,
    this.loadingBuilder,
    this.emptyBuilder,
    this.errorBuilder,
    this.onRetry,
    this.emptyMessage,
    this.emptyIcon,
  });

  @override
  State<EntityStateWidget> createState() => _EntityStateWidgetState<T>();
}

class _EntityStateWidgetState<T> extends State<EntityStateWidget<T>> {
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final viewModel = widget.model.value;

      return switch (viewModel.state) {
        ViewState.loading => _buildLoadingState(),
        ViewState.content => _buildContentState(viewModel.data),
        ViewState.empty => _buildEmptyState(viewModel.errorMessage),
        ViewState.error => _buildErrorState(viewModel.errorMessage),
      };
    });
  }

  Widget _buildLoadingState() {
    return widget.loadingBuilder?.call() ?? const DefaultLoadingWidget();
  }

  Widget _buildContentState(T? data) {
    if (data == null) {
      return _buildEmptyState('No data available');
    }

    return widget.itemBuilder?.call(data) ??
           Text('Content: ${data.toString()}');
  }

  Widget _buildEmptyState(String? message) {
    final emptyMessage = message ?? widget.emptyMessage ?? 'No data available';

    return widget.emptyBuilder?.call(emptyMessage) ??
           DefaultEmptyWidget(
             message: emptyMessage,
             icon: widget.emptyIcon,
           );
  }

  Widget _buildErrorState(String? message) {
    final errorMessage = message ?? 'An error occurred';

    return widget.errorBuilder?.call(errorMessage, widget.onRetry) ??
           DefaultErrorWidget(
             message: errorMessage,
             onRetry: widget.onRetry,
           );
  }
}

enum ViewState { loading, content, empty, error }

class ViewModel<T> {
  final ViewState state;
  final T? data;
  final String? errorMessage;

  const ViewModel.loading()
      : state = ViewState.loading,
        data = null,
        errorMessage = null;

  const ViewModel.content(this.data)
      : state = ViewState.content,
        errorMessage = null;

  const ViewModel.empty()
      : state = ViewState.empty,
        data = null,
        errorMessage = null;

  const ViewModel.error(this.errorMessage)
      : state = ViewState.error,
        data = null;
}

class PaginatedViewModel<T> extends ViewModel<List<T>> {
  final int currentPage;
  final bool hasMore;

  const PaginatedViewModel.loading()
      : currentPage = 1,
        hasMore = false,
        super.loading();

  const PaginatedViewModel.content(
    List<T> super.data, {
    this.currentPage = 1,
    this.hasMore = false,
  }) : super.content();

  const PaginatedViewModel.error(String super.message)
      : currentPage = 1,
        hasMore = false,
        super.error();

  const PaginatedViewModel.empty()
      : currentPage = 1,
        hasMore = false,
        super.empty();
}

// Default UI Widgets
class DefaultLoadingWidget extends StatelessWidget {
  const DefaultLoadingWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(32.0),
        child: CircularProgressIndicator(),
      ),
    );
  }
}

class DefaultEmptyWidget extends StatelessWidget {
  final String message;
  final Widget? icon;

  const DefaultEmptyWidget({
    super.key,
    required this.message,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            icon ??
            const Icon(
              Icons.inbox_outlined,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              message,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.grey,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

class DefaultErrorWidget extends StatelessWidget {
  final String message;
  final VoidCallback? onRetry;

  const DefaultErrorWidget({
    super.key,
    required this.message,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              message,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.red,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
            if (onRetry != null) ...[
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh),
                label: const Text('Retry'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
