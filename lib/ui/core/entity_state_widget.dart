import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:darve/services/error/error_models.dart';

typedef WidgetCallback<T> = Widget Function(T data);
typedef EmptyWidgetBuilder = Widget Function(String? message);
typedef ErrorWidgetBuilder = Widget Function(String? message, VoidCallback? onRetry);
typedef LoadingWidgetBuilder = Widget Function();

class EntityStateWidget<T> extends StatefulWidget {
  final Rx<ViewModel<T>> model;
  final WidgetCallback<T>? itemBuilder;
  final LoadingWidgetBuilder? loadingBuilder;
  final EmptyWidgetBuilder? emptyBuilder;
  final ErrorWidgetBuilder? errorBuilder;
  final VoidCallback? onRetry;
  final String? emptyMessage;
  final Widget? emptyIcon;

  const EntityStateWidget({
    super.key,
    required this.model,
    this.itemBuilder,
    this.loadingBuilder,
    this.emptyBuilder,
    this.errorBuilder,
    this.onRetry,
    this.emptyMessage,
    this.emptyIcon,
  });

  @override
  State<EntityStateWidget> createState() => _EntityStateWidgetState<T>();
}

class _EntityStateWidgetState<T> extends State<EntityStateWidget<T>> {
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final viewModel = widget.model.value;

      return switch (viewModel.state) {
        ViewState.loading => _buildLoadingState(),
        ViewState.content => _buildContentState(viewModel.data),
        ViewState.empty => _buildEmptyState(viewModel.errorMessage),
        ViewState.error => _buildErrorState(viewModel.error),
      };
    });
  }

  Widget _buildLoadingState() {
    return widget.loadingBuilder?.call() ?? const DefaultLoadingWidget();
  }

  Widget _buildContentState(T? data) {
    if (data == null) {
      return _buildEmptyState('No data available');
    }

    return widget.itemBuilder?.call(data) ??
           Text('Content: ${data.toString()}');
  }

  Widget _buildEmptyState(String? message) {
    final emptyMessage = message ?? widget.emptyMessage ?? 'No data available';

    return widget.emptyBuilder?.call(emptyMessage) ??
           DefaultEmptyWidget(
             message: emptyMessage,
             icon: widget.emptyIcon,
           );
  }

  Widget _buildErrorState(dynamic error) {
    return widget.errorBuilder?.call(error, widget.onRetry) ??
           DefaultErrorWidget(
             error: error,
             onRetry: widget.onRetry,
           );
  }
}

enum ViewState { loading, content, empty, error }

class ViewModel<T> {
  final ViewState state;
  final T? data;
  final dynamic error; // Changed from String? errorMessage to dynamic error

  const ViewModel.loading()
      : state = ViewState.loading,
        data = null,
        error = null;

  const ViewModel.content(this.data)
      : state = ViewState.content,
        error = null;

  const ViewModel.empty()
      : state = ViewState.empty,
        data = null,
        error = null;

  const ViewModel.error(this.error)
      : state = ViewState.error,
        data = null;

  /// Get user-friendly error message
  String get errorMessage {
    if (error == null) return 'An error occurred';

    if (error is AppError) {
      return (error as AppError).message;
    }

    if (error is String) {
      return error as String;
    }

    return error.toString();
  }

  /// Get detailed error information for debugging
  String get debugErrorInfo {
    if (error == null) return 'No error information available';

    if (error is AppError) {
      final appError = error as AppError;
      return '''
Error Type: ${appError.runtimeType}
Message: ${appError.message}
Code: ${appError.code ?? 'N/A'}
Severity: ${appError.severity.name}
Request ID: ${appError.requestId ?? 'N/A'}
Metadata: ${appError.metadata ?? 'N/A'}
Original Error: ${appError.originalError ?? 'N/A'}
''';
    }

    return 'Error: ${error.toString()}\nType: ${error.runtimeType}';
  }
}

class PaginatedViewModel<T> extends ViewModel<List<T>> {
  final int currentPage;
  final bool hasMore;

  const PaginatedViewModel.loading()
      : currentPage = 1,
        hasMore = false,
        super.loading();

  const PaginatedViewModel.content(
    List<T> super.data, {
    this.currentPage = 1,
    this.hasMore = false,
  }) : super.content();

  const PaginatedViewModel.error(dynamic super.error)
      : currentPage = 1,
        hasMore = false,
        super.error();

  const PaginatedViewModel.empty()
      : currentPage = 1,
        hasMore = false,
        super.empty();
}

// Default UI Widgets
class DefaultLoadingWidget extends StatelessWidget {
  const DefaultLoadingWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(32.0),
        child: CircularProgressIndicator(),
      ),
    );
  }
}

class DefaultEmptyWidget extends StatelessWidget {
  final String message;
  final Widget? icon;

  const DefaultEmptyWidget({
    super.key,
    required this.message,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            icon ??
            const Icon(
              Icons.inbox_outlined,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              message,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.grey,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

class DefaultErrorWidget extends StatelessWidget {
  final dynamic error;
  final VoidCallback? onRetry;

  const DefaultErrorWidget({
    super.key,
    this.error,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    final userMessage = _getUserMessage();
    final debugInfo = _getDebugInfo();

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              _getErrorIcon(),
              size: 64,
              color: _getErrorColor(),
            ),
            const SizedBox(height: 16),
            Text(
              userMessage,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.black87,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),

            // Show debug information in debug mode
            if (kDebugMode && debugInfo.isNotEmpty) ...[
              const SizedBox(height: 16),
              ExpansionTile(
                title: const Text(
                  'Debug Information',
                  style: TextStyle(fontSize: 14, color: Colors.grey),
                ),
                children: [
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    margin: const EdgeInsets.symmetric(horizontal: 16),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey[300]!),
                    ),
                    child: Text(
                      debugInfo,
                      style: const TextStyle(
                        fontSize: 12,
                        fontFamily: 'monospace',
                        color: Colors.black87,
                      ),
                    ),
                  ),
                ],
              ),
            ],

            if (onRetry != null) ...[
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh),
                label: const Text('Retry'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  String _getUserMessage() {
    if (error == null) return 'An error occurred';

    if (error is AppError) {
      return (error as AppError).message;
    }

    if (error is String) {
      return error as String;
    }

    return 'Something went wrong. Please try again.';
  }

  String _getDebugInfo() {
    if (error == null) return 'No error information available';

    if (error is AppError) {
      final appError = error as AppError;
      return '''
Error Type: ${appError.runtimeType}
Message: ${appError.message}
Code: ${appError.code ?? 'N/A'}
Severity: ${appError.severity.name}
Request ID: ${appError.requestId ?? 'N/A'}
Metadata: ${appError.metadata ?? 'N/A'}
Original Error: ${appError.originalError ?? 'N/A'}
''';
    }

    return 'Error: ${error.toString()}\nType: ${error.runtimeType}';
  }

  IconData _getErrorIcon() {
    if (error is AppError) {
      final appError = error as AppError;
      switch (appError.severity) {
        case ErrorSeverity.warning:
          return Icons.warning_outlined;
        case ErrorSeverity.fatal:
          return Icons.error;
        case ErrorSeverity.expected:
          return Icons.info_outline;
        default:
          return Icons.error_outline;
      }
    }

    return Icons.error_outline;
  }

  Color _getErrorColor() {
    if (error is AppError) {
      final appError = error as AppError;
      switch (appError.severity) {
        case ErrorSeverity.warning:
          return Colors.orange[300]!;
        case ErrorSeverity.fatal:
          return Colors.red[600]!;
        case ErrorSeverity.expected:
          return Colors.blue[300]!;
        default:
          return Colors.red[300]!;
      }
    }

    return Colors.red[300]!;
  }
}
