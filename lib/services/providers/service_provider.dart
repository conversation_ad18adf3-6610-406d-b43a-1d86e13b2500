import 'package:darve/api/repositories/notification_repository.dart';
import 'package:get_it/get_it.dart';
import 'package:darve/services/auth/auth_service.dart';
import 'package:darve/services/auth/auth_service_impl.dart';
import 'package:darve/services/http/http_service.dart';

import 'package:darve/api/repositories/auth_repository.dart';
import 'package:darve/api/repositories/user_repository.dart';
import 'package:darve/api/repositories/profile_repository.dart';
import 'package:darve/api/repositories/legacy_profile_repository.dart';
import 'package:darve/api/repositories/challenge_repository.dart';
import 'package:darve/api/repositories/wallet_repository.dart';
import 'package:darve/api/repositories/chat_repository.dart';
import 'package:darve/api/repositories/posts_repository.dart';


class ServiceProvider {
  static final GetIt _getIt = GetIt.instance;
  static bool _isInitialized = false;

  static GetIt get instance => _getIt;

  static Future<void> initialize() async {
    if (_isInitialized) return;

    // Register DioService (depends on SimpleAuthService)
    _getIt.registerLazySingleton<HttpService>(
      () => HttpService(),
    );

    // Register Repository Interfaces with their implementations
    _getIt.registerLazySingleton<AuthRepository>(
      () => AuthRepository(_getIt<HttpService>()),
    );

    _getIt.registerLazySingleton<UserRepository>(
      () => UserRepository(_getIt<HttpService>()),
    );

    _getIt.registerLazySingleton<ProfileRepository>(
      () => ProfileRepository(_getIt<HttpService>()),
    );

    _getIt.registerLazySingleton<LegacyProfileRepository>(
      () => LegacyProfileRepository(_getIt<HttpService>()),
    );

    _getIt.registerLazySingleton<ChallengeRepository>(
      () => ChallengeRepository(_getIt<HttpService>()),
    );

    _getIt.registerLazySingleton<WalletRepository>(
      () => WalletRepository(_getIt<HttpService>()),
    );

    _getIt.registerLazySingleton<ChatRepository>(
      () => ChatRepository(_getIt<HttpService>()),
    );

    _getIt.registerLazySingleton<PostsRepository>(
      () => PostsRepository(_getIt<HttpService>()),
    );

    _getIt.registerLazySingleton<NotificationRepository>(
      () => NotificationRepository(_getIt<HttpService>()),
    );



    // Step 4: Register full AuthService (depends on repositories)
    _getIt.registerLazySingleton<AuthService>(() => AuthServiceImpl(
          authRepository: _getIt<AuthRepository>(),
        ));

    _isInitialized = true;
  }

  // Convenience getters
  static AuthService get authService => _getIt<AuthService>();

  static HttpService get dioService => _getIt<HttpService>();

  static AuthRepository get authRepository => _getIt<AuthRepository>();

  static UserRepository get userRepository => _getIt<UserRepository>();

  static ProfileRepository get profileRepository => _getIt<ProfileRepository>();

  static LegacyProfileRepository get legacyProfileRepository => _getIt<LegacyProfileRepository>();

  static ChallengeRepository get challengeRepository =>
      _getIt<ChallengeRepository>();

  static WalletRepository get walletRepository => _getIt<WalletRepository>();

  static ChatRepository get chatRepository => _getIt<ChatRepository>();

  static PostsRepository get postsRepository => _getIt<PostsRepository>();

  static NotificationRepository get notificationRepository => _getIt<NotificationRepository>();

  static void reset() {
    _getIt.reset();
    _isInitialized = false;
  }

  static bool get isInitialized => _isInitialized;
}
