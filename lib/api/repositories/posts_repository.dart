import 'package:darve/services/http/http_service.dart';
import 'package:darve/api/models/post_model.dart';
import 'package:dio/dio.dart';

import '../../utils/utility.dart';
import 'package:darve/utils/constants.dart';

class PostsRepository {
  final HttpService _dioService;

  PostsRepository(this._dioService);

  
  Future<List<PostModel>> getPosts(String username) async {
    final response = await _dioService.get(ApiPaths.getPosts(username));
    
    List<PostModel> allPosts = [];
    for (var element in response.data['posts']) {
      allPosts.add(PostModel.fromJson(element));
    }
    return allPosts;
  }

  
  Future<List<PostModel>> getFollowingPosts() async {
    final response = await _dioService.get(ApiPaths.getFollowingPosts);
    
    List<PostModel> postsList = [];
    for (var post in response.data['post_list']) {
      postsList.add(PostModel.fromJson(post));
    }
    return postsList;
  }

  
  Future<dynamic> createPost(String content, {String filePath = ""}) async {
    final postTitle = generateRandomTitle(32);
    
    final formData = FormData.fromMap({
      'title': postTitle,
      'content': content,
      'topic_id': '',
    });

    if (filePath.isNotEmpty) {
      formData.files.add(MapEntry(
        'file',
        await MultipartFile.fromFile(filePath),
      ));
    }

    final response = await _dioService.post(ApiPaths.createPost, data: formData);
    return response.data;
  }

  
  // Get post discussions/comments
  Future<dynamic> getPostDiscussions(String discussionId, String postId) async {
    final response = await _dioService.get(ApiPaths.getPostDiscussions(discussionId, postId));
    return response.data;
  }

  Future<bool> postInDiscussion(String discussionId, String postUri, String content) async {
    final postTitle = generateRandomTitle(32);

    final response = await _dioService.post(
      ApiPaths.postInDiscussion(discussionId, postUri),
      data: {
        'title': content,
        'content': postTitle,
      },
    );
    
    return response.statusCode == 200;
  }

  
  Future<dynamic> likePost(String postId) async {
    final response = await _dioService.post(ApiPaths.likePost(postId));
    return response.data;
  }

  
  Future<dynamic> unlikePost(String postId) async {
    final response = await _dioService.delete(ApiPaths.unlikePost(postId));
    return response.data;
  }

  
  Future<dynamic> sharePost(String postId) async {
    final response = await _dioService.post(ApiPaths.sharePost(postId));
    return response.data;
  }

  
  Future<dynamic> deletePost(String postId) async {
    final response = await _dioService.delete(ApiPaths.deletePost(postId));
    return response.data;
  }

  
  Future<dynamic> editPost(String postId, String newContent) async {
    final response = await _dioService.put(
      ApiPaths.editPost(postId),
      data: {'content': newContent},
    );
    return response.data;
  }

  
  Future<dynamic> getPostComments(String postId) async {
    final response = await _dioService.get(ApiPaths.getPostComments(postId));
    return response.data;
  }

  
  Future<dynamic> addComment(String postId, String comment) async {
    final response = await _dioService.post(
      ApiPaths.addComment(postId),
      data: {'content': comment},
    );
    return response.data;
  }
}
