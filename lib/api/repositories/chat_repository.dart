import 'package:darve/services/http/http_service.dart';
import 'package:darve/utils/utility.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:darve/utils/constants.dart';

class ChatRepository {
  final HttpService _dioService;

  ChatRepository(this._dioService);

  
  Future<dynamic> getChatsList() async {
    final response = await _dioService.get(ApiPaths.getChatsList);
    return response.data;
  }

  
  Future<dynamic> createChatWithUserId(String otherUserId) async {
    final response = await _dioService.get(ApiPaths.createChatWithUserId(otherUserId));
    return response.data;
  }

  
  Future<dynamic> postMessageInChat(String discussionId, String content) async {
    final postTitle = generateRandomTitle(32);
    
    final formData = FormData.fromMap({
      'title': postTitle,
      'content': content,
      'topic_id': '',
    });

    final response = await _dioService.post(
      ApiPaths.postMessageInChat(discussionId),
      data: formData,
    );
    return response.data;
  }

  
  Future<dynamic> getChatMessages(String chatId, {int limit = 50, int offset = 0}) async {
    final response = await _dioService.get(
      ApiPaths.getChatMessages(chatId),
      queryParameters: {
        'limit': limit,
        'offset': offset,
      },
    );
    return response.data;
  }

  
  Future<dynamic> markChatAsRead(String chatId) async {
    final response = await _dioService.post(ApiPaths.markChatAsRead(chatId));
    return response.data;
  }

  
  Future<dynamic> deleteChatMessage(String messageId) async {
    final response = await _dioService.delete(ApiPaths.deleteChatMessage(messageId));
    return response.data;
  }

  
  Future<dynamic> editChatMessage(String messageId, String newContent) async {
    final response = await _dioService.put(
      ApiPaths.editChatMessage(messageId),
      data: {'content': newContent},
    );
    return response.data;
  }

  Stream<String?> getChatDiscussionByIdSse(String discussionId) {
    return _dioService.getStream(ApiPaths.getChatDiscussionByIdSse(discussionId));
  }

  Future<List> getChatDiscussionById(String discussionId) async {
    final response = await _dioService.get(
      ApiPaths.getChatDiscussionById(discussionId),
    );

    return response.data as List;
  }

  Stream<dynamic> getChatListSse() async* {
    var emittedCount = 0;
    while (true) {
      emittedCount++;
      try {
        yield* _dioService.getStream(ApiPaths.getChatListSse);
      } catch (error) {
        debugPrint(
            'emittedCount: $emittedCount Error during getChatListSse: $error');
        yield {error: error, emittedCount: emittedCount};

        // Wait a bit before retrying to prevent rapid reconnections
        await Future.delayed(const Duration(seconds: 1));
      }
    }
  }
}
