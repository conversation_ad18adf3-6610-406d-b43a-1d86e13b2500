import 'package:darve/services/http/http_service.dart';
import 'package:darve/utils/constants.dart';

class UserRepository {
  final HttpService _dioService;

  UserRepository(this._dioService);

  Future<void> setPassword(String password) async {
    await _dioService.post(
      ApiPaths.setPassword,
      data: {'password': password},
    );
  }

  Future<void> changePassword(String oldPassword, String newPassword) async {
    await _dioService.post(
      ApiPaths.changePassword,
      data: {
        'old_password': oldPassword,
        'new_username': newPassword, // Note: keeping original field name
      },
    );
  }

  Future<void> emailVerificationStart(String email) async {
    await _dioService.post(
      ApiPaths.emailVerificationStart,
      data: {'email': email},
    );
  }

  Future<void> emailVerificationConfirm(String email, String code) async {
    await _dioService.post(
      ApiPaths.emailVerificationConfirm,
      data: {
        'email': email,
        'code': code,
      },
    );
  }
}
