import 'package:darve/services/http/http_service.dart';
import 'package:dio/dio.dart';
import 'package:darve/utils/constants.dart';

class ChallengeRepository {
  final HttpService _dioService;

  ChallengeRepository(this._dioService);

  Future<dynamic> getTaskRequests(String postId) async {
    final response = await _dioService.get(ApiPaths.getTaskRequests(postId));
    return response.data;
  }

  
  Future<dynamic> getAllReceivedTaskRequests() async {
    final response = await _dioService.get(ApiPaths.getAllReceivedTaskRequests);
    return response.data;
  }

  
  Future<dynamic> getReceivedTaskRequestsForPost(String postId) async {
    final response = await _dioService.get(ApiPaths.getReceivedTaskRequestsForPost(postId));
    return response.data;
  }

  
  Future<dynamic> acceptChallenge(String taskId, bool accepted) async {
    final response = await _dioService.post(
      ApiPaths.acceptChallenge(taskId),
      data: {'accept': accepted},
    );
    return response.data;
  }

  
  Future<dynamic> deliverTaskRequest(String taskId, String filePath, String postId) async {
    final formData = FormData.fromMap({
      'post_id': postId,
      'file': await MultipartFile.fromFile(filePath),
    });

    final response = await _dioService.post(
      ApiPaths.deliverTaskRequest(taskId),
      data: formData,
    );
    return response.data;
  }

  
  Future<dynamic> createChallenge(Map<String, dynamic> challengeData) async {
    final response = await _dioService.post(ApiPaths.createChallenge, data: challengeData);
    return response.data;
  }

  
  Future<dynamic> getMyChallenges() async {
    final response = await _dioService.get(ApiPaths.getMyChallenges);
    return response.data;
  }

  
  Future<dynamic> getGivenChallenges() async {
    final response = await _dioService.get(ApiPaths.getGivenChallenges);
    return response.data;
  }

  
  Future<dynamic> addParticipant(String challengeId, int amount) async {
    final response = await _dioService.post(
      ApiPaths.addParticipant(challengeId),
      data: {'amount': amount},
    );
    return response.data;
  }

  // Create task request method
  Future<dynamic> createTaskRequest({
    required String toUserId,
    required String content,
    required String postId,
    required double offerAmount,
  }) async {
    final response = await _dioService.post(
      ApiPaths.createTaskRequest,
      data: {
        'to_user': toUserId,
        'content': content,
        'post_id': postId,
        'offer_amount': offerAmount.toInt(),
      },
    );
    return response.data;
  }

  // Get given task requests for post
  Future<dynamic> getGivenTaskRequestsForPost(String postId) async {
    final response = await _dioService.get(ApiPaths.getGivenTaskRequestsForPost(postId));
    return response.data;
  }
}
