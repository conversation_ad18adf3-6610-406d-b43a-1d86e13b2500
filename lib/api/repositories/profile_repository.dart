import 'package:darve/api/models/follower_model.dart';
import 'package:darve/api/models/profile_data_model.dart';
import 'package:darve/services/http/http_service.dart';
import 'package:darve/utils/constants.dart';

class ProfileRepository {
  final HttpService _dioService;

  ProfileRepository(this._dioService);

  Future<ProfileDataModel> getProfileData(String username) async {
    if (username.startsWith("@")) {
      username = username.split("@")[1];
    }

    final response = await _dioService.get(ApiPaths.userProfile(username));

    if (response.data != null) {
      return ProfileDataModel.fromJson(response.data);
    }
    return ProfileDataModel.empty();
  }

  Future<dynamic> followUser(String userId) async {
    final response = await _dioService.post(ApiPaths.followUser(userId));
    return response.data;
  }

  Future<dynamic> unfollowUser(String userId) async {
    final response = await _dioService.post(ApiPaths.unfollowUser(userId));
    return response.data;
  }

  Future<dynamic> searchUser(String userInput) async {
    final response = await _dioService.post(
      ApiPaths.searchUser,
      data: {'query': userInput},
    );

    return response.data['items'] ?? [];
  }

  Future<List<FollowerModel>> getFollowers(String userId) async {
    final response = await _dioService.get(ApiPaths.getFollowers(userId));

    if (response.data == null || response.data['items'] == null) {
      return [];
    }

    // Parse followers list from response
    List<FollowerModel> followers = (response.data['items'] as List)
        .map((item) => FollowerModel(
              username: item['username'] ?? '',
              name: item['name'] ?? '',
              imageUrl: item['image_url'] ?? '',
            ))
        .toList();
    return followers;
  }

  Future<bool> isFollowing(String userId) async {
    final response = await _dioService.get(ApiPaths.isFollowing(userId));
    return response.data['is_following'] ?? false;
  }

  Future<dynamic> editProfile(String profileData, String imagePath) async {
    final response = await _dioService.post(
      ApiPaths.editProfile,
      data: profileData,
    );
    return response.data;
  }

  Future<List<FollowerModel>> getFollowing(String userId) async {
    final response = await _dioService.get(ApiPaths.getFollowing(userId));

    if (response.data == null || response.data['items'] == null) {
      return [];
    }

    // Parse following list from response
    List<FollowerModel> following = (response.data['items'] as List)
        .map((item) => FollowerModel(
          username: item['username'] ?? '',
          name: item['name'] ?? '',
          imageUrl: item['image_url'] ?? '',
        ))
        .toList();
    return following;
  }
}
