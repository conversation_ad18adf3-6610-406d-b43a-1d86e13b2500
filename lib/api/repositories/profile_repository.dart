import 'package:darve/api/models/follower_model.dart';
import 'package:darve/api/models/profile_data_model.dart';
import 'package:darve/services/http/http_service.dart';
import 'package:darve/services/error/error_models.dart';
import 'package:darve/utils/constants.dart';
import 'package:darve/utils/result.dart';

class ProfileRepository {
  final HttpService _dioService;

  ProfileRepository(this._dioService);

  /// Get profile data using Result type for better error handling
  Future<Result<ProfileDataModel>> getProfileDataResult(String username) async {
    return ResultHelper.tryCallAsync(
      () async {
        if (username.startsWith("@")) {
          username = username.split("@")[1];
        }

        final response = await _dioService.get(ApiPaths.userProfile(username));

        if (response.data != null) {
          return ProfileDataModel.fromJson(response.data);
        }
        return ProfileDataModel.empty();
      },
      errorMessage: 'Unable to load profile data. Please try again.',
      errorCode: 'PROFILE_LOAD_ERROR',
      metadata: {
        'username': username,
        'operation': 'getProfileData',
      },
    );
  }

  Future<dynamic> followUser(String userId) async {
    final response = await _dioService.post(ApiPaths.followUser(userId));
    return response.data;
  }

  /// Get followers using Result type for better error handling
  Future<Result<List<FollowerModel>>> getFollowersResult(String userId) async {
    return ResultHelper.tryCallAsync(
      () async {
        final response = await _dioService.get(ApiPaths.getFollowers(userId));

        if (response.data == null || response.data['items'] == null) {
          return <FollowerModel>[];
        }

        // Parse followers list from response
        List<FollowerModel> followers = (response.data['items'] as List)
            .map((item) => FollowerModel(
                  username: item['username'] ?? '',
                  name: item['name'] ?? '',
                  imageUrl: item['image_url'] ?? '',
                ))
            .toList();
        return followers;
      },
      errorMessage: 'Unable to load followers. Please try again.',
      errorCode: 'FOLLOWERS_LOAD_ERROR',
      metadata: {
        'userId': userId,
        'operation': 'getFollowers',
      },
    );
  }

  /// Get following list using Result type for better error handling
  Future<Result<List<FollowerModel>>> getFollowingResult(String userId) async {
    return ResultHelper.tryCallAsync(
      () async {
        final response = await _dioService.get(ApiPaths.getFollowing(userId));

        if (response.data == null || response.data['items'] == null) {
          return <FollowerModel>[];
        }

        // Parse following list from response
        List<FollowerModel> following = (response.data['items'] as List)
            .map((item) => FollowerModel(
              username: item['username'] ?? '',
              name: item['name'] ?? '',
              imageUrl: item['image_url'] ?? '',
            ))
            .toList();
        return following;
      },
      errorMessage: 'Unable to load following list. Please try again.',
      errorCode: 'FOLLOWING_LOAD_ERROR',
      metadata: {
        'userId': userId,
        'operation': 'getFollowing',
      },
    );
  }

  /// Unfollow user using Result type for better error handling
  Future<Result<dynamic>> unfollowUserResult(String userId) async {
    return ResultHelper.tryCallAsync(
      () async {
        final response = await _dioService.post(ApiPaths.unfollowUser(userId));
        return response.data;
      },
      errorMessage: 'Unable to unfollow user. Please try again.',
      errorCode: 'UNFOLLOW_ERROR',
      metadata: {
        'userId': userId,
        'operation': 'unfollowUser',
      },
    );
  }

  Future<dynamic> searchUser(String userInput) async {
    final response = await _dioService.post(
      ApiPaths.searchUser,
      data: {'query': userInput},
    );

    return response.data['items'] ?? [];
  }



  Future<bool> isFollowing(String userId) async {
    final response = await _dioService.get(ApiPaths.isFollowing(userId));
    return response.data['is_following'] ?? false;
  }

  Future<dynamic> editProfile(String profileData, String imagePath) async {
    final response = await _dioService.post(
      ApiPaths.editProfile,
      data: profileData,
    );
    return response.data;
  }


}
