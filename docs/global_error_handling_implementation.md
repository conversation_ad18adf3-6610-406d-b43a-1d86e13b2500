# Global Error Handling Implementation

## Overview

Successfully implemented proper global error handling architecture where repositories catch and format errors using the existing error infrastructure, eliminating the need for each controller to manage error parsing and formatting.

## Architecture Layers

### 1. **HTTP Layer - Error Interception**
The `ErrorInterceptor` automatically parses Dio exceptions into structured `AppError` objects:

```dart
// lib/services/http/interceptors/error_interceptor.dart
@override
void onError(DioException err, ErrorInterceptorHandler handler) {
  // Parse the error into our structured format
  final appError = ErrorParser.parseFromDioException(err);
  
  // Create enhanced error with parsed AppError
  final enhancedError = DioException(
    requestOptions: err.requestOptions,
    response: err.response,
    type: err.type,
    error: appError, // Replace original error with parsed AppError
    message: appError.message,
    stackTrace: err.stackTrace,
  );

  handler.next(enhancedError);
}
```

### 2. **Repository Layer - Error Catching and Formatting**
Repositories catch all exceptions and format them properly using global error classes:

```dart
// ✅ CORRECT: Repository handles error formatting
Future<List<FollowerModel>> getFollowers(String userId) async {
  try {
    final response = await _dioService.get(ApiPaths.getFollowers(userId));
    // ... parse response
    return followers;
  } catch (e) {
    // If it's already an AppError (from HTTP interceptor), re-throw it
    if (e is AppError) {
      throw e;
    }
    
    // For unexpected errors, wrap in UserError with context
    throw UserError(
      message: 'Unable to load followers. Please try again.',
      code: 'FOLLOWERS_LOAD_ERROR',
      originalError: e,
      metadata: {
        'userId': userId,
        'operation': 'getFollowers',
      },
    );
  }
}
```

### 3. **Controller Layer - Simple Error Handling**
Controllers no longer need custom error parsing - they just handle the formatted errors:

```dart
// ✅ SIMPLIFIED: Controller just handles formatted errors
Future<void> loadFollowers() async {
  try {
    followersState.value = const ViewModel<List<FollowerModel>>.loading();
    final followersList = await ServiceProvider.profileRepository.getFollowers(userId);
    
    if (followersList.isEmpty) {
      followersState.value = const ViewModel<List<FollowerModel>>.empty();
    } else {
      followersState.value = ViewModel<List<FollowerModel>>.content(followersList);
    }
  } catch (e) {
    // Repository already formatted the error properly
    final errorMessage = e.toString();
    followersState.value = ViewModel<List<FollowerModel>>.error(errorMessage);
    _errorHandler.displayErrorToast(e, 'loadFollowers');
  }
}
```

## Error Types and Formatting

### **Network Errors**
Automatically handled by `ErrorParser` for common HTTP scenarios:

```dart
// 404 Not Found
NetworkError(
  message: 'Resource not found',
  statusCode: 404,
  code: 'NOT_FOUND',
  endpoint: '/api/user/123/followers',
  method: 'GET',
)

// 500 Server Error
NetworkError.serverError(
  statusCode: 500,
  message: 'Internal server error',
  endpoint: '/api/user/123/followers',
  method: 'GET',
)

// Timeout
NetworkError.timeout()
// message: 'Connection timeout. Please check your internet connection.'
```

### **Authentication Errors**
Automatically detected and formatted:

```dart
// 401 Unauthorized
AuthError(
  message: 'Unauthorized. Please log in again.',
  code: 'UNAUTHORIZED',
  severity: ErrorSeverity.warning,
)

// Session Expired
AuthError.sessionExpired()
// message: 'Session Expired! Log in again'
```

### **User Errors**
For business logic and user-facing errors:

```dart
// Repository wraps unexpected errors
UserError(
  message: 'Unable to load followers. Please try again.',
  code: 'FOLLOWERS_LOAD_ERROR',
  originalError: originalException,
  metadata: {
    'userId': userId,
    'operation': 'getFollowers',
  },
)

// Predefined user errors
UserError.contentTooShort()
// message: 'Content length too short, need at least 5 characters'

UserError.alreadyFollowing()
// message: 'Already following the user!'
```

## Repository Error Handling Pattern

### **Standard Pattern**
All repository methods follow this consistent pattern:

```dart
Future<T> repositoryMethod(params) async {
  try {
    // Perform API call
    final response = await _dioService.method(endpoint);
    
    // Parse and return data
    return parseResponse(response);
  } catch (e) {
    // If it's already an AppError (from HTTP interceptor), re-throw it
    if (e is AppError) {
      throw e;
    }
    
    // For unexpected errors, wrap in appropriate error type
    throw UserError(
      message: 'User-friendly error message',
      code: 'OPERATION_ERROR',
      originalError: e,
      metadata: {
        'operation': 'methodName',
        'params': params,
      },
    );
  }
}
```

### **Benefits of This Pattern**

#### ✅ **Consistent Error Messages**
- All similar operations have the same error messages
- User-friendly messages are defined once in the repository
- No duplication of error handling logic across controllers

#### ✅ **Proper Error Context**
- Metadata includes operation details for debugging
- Original errors are preserved for technical analysis
- Request IDs are maintained for correlation

#### ✅ **Separation of Concerns**
- Repository: Data access and error formatting
- Controller: Business logic and state management
- UI: Error display and user interaction

#### ✅ **Global Error Infrastructure**
- Leverages existing `ErrorParser` for HTTP errors
- Uses structured `AppError` classes for consistency
- Integrates with error reporting service

## Error Display Flow

### **1. Repository Formats Error**
```dart
// Repository catches and formats
throw UserError(
  message: 'Unable to load followers. Please try again.',
  code: 'FOLLOWERS_LOAD_ERROR',
);
```

### **2. Controller Receives Formatted Error**
```dart
// Controller gets user-friendly error
catch (e) {
  final errorMessage = e.toString(); // "Unable to load followers. Please try again."
  followersState.value = ViewModel.error(errorMessage);
  _errorHandler.displayErrorToast(e);
}
```

### **3. UI Displays Error**
```dart
// EntityStateWidget shows formatted message
EntityStateWidget<List<FollowerModel>>(
  model: controller.followersState,
  onRetry: () => controller.loadFollowers(),
  // Error state automatically shows: "Unable to load followers. Please try again."
  // With retry button that calls controller.loadFollowers()
)
```

## Migration Benefits

### **Before (Controller Error Parsing)**
```dart
// ❌ Each controller had custom error parsing
String _getErrorMessage(dynamic error) {
  if (error.toString().contains('404')) return 'User not found';
  if (error.toString().contains('403')) return 'Access denied';
  if (error.toString().contains('timeout')) return 'Connection timeout';
  return 'Something went wrong';
}
```

### **After (Global Error Formatting)**
```dart
// ✅ Repository handles all error formatting
// ✅ Controllers just use the formatted errors
// ✅ Consistent messages across the app
// ✅ No duplication of error parsing logic
```

## Error Reporting Integration

### **Automatic Error Reporting**
The global error system automatically reports errors to Sentry:

```dart
// HTTP Interceptor automatically reports
_reportError(appError, err);

// Repository errors include context
throw UserError(
  message: 'Unable to load followers. Please try again.',
  code: 'FOLLOWERS_LOAD_ERROR',
  originalError: e,
  metadata: {
    'userId': userId,
    'operation': 'getFollowers',
  },
);
```

### **Error Breadcrumbs**
User actions are tracked for debugging:

```dart
// Automatic breadcrumb when error is displayed
_errorReporting.addBreadcrumb(
  message: 'Error displayed to user: FOLLOWERS_LOAD_ERROR',
  category: 'ui.error',
  data: {
    'error_message': 'Unable to load followers. Please try again.',
    'error_code': 'FOLLOWERS_LOAD_ERROR',
    'function_name': 'loadFollowers',
  },
);
```

## Testing Benefits

### **Repository Tests**
```dart
test('should throw UserError when API fails', () async {
  // Mock API failure
  when(mockDioService.get(any)).thenThrow(Exception('Network error'));
  
  expect(
    () => repository.getFollowers('user123'),
    throwsA(isA<UserError>()
        .having((e) => e.message, 'message', 'Unable to load followers. Please try again.')
        .having((e) => e.code, 'code', 'FOLLOWERS_LOAD_ERROR')),
  );
});
```

### **Controller Tests**
```dart
test('should set error state when repository throws', () async {
  // Mock repository error
  when(mockRepository.getFollowers(any))
      .thenThrow(UserError(message: 'Test error', code: 'TEST_ERROR'));
  
  await controller.loadFollowers();
  
  expect(controller.followersState.value.state, ViewState.error);
  expect(controller.followersState.value.errorMessage, 'Test error');
});
```

## Implementation Checklist

- [x] Repository methods catch all exceptions
- [x] Repository methods format errors using global error classes
- [x] Repository methods preserve original errors for debugging
- [x] Repository methods include operation context in metadata
- [x] Controllers removed custom error parsing logic
- [x] Controllers use repository-formatted error messages
- [x] Error messages are user-friendly and actionable
- [x] Error reporting integration is maintained
- [x] Consistent error handling across all repository methods

This implementation provides a robust, maintainable error handling system that eliminates duplication and ensures consistent user experience across the application.
