# Result to ViewModel Standard Pattern

## Overview

Established a standard functional approach for converting Result types to ViewModel states in controllers, eliminating imperative assignments inside fold callbacks and creating cleaner, more declarative code.

## Standard Pattern

### ✅ **Recommended: Direct Assignment from fold()**

```dart
/// Load followers list
Future<void> loadFollowers() async {
  followersState.value = const ViewModel<List<FollowerModel>>.loading();

  final result = await ServiceProvider.profileRepository.getFollowersResult(userId);

  // ✅ STANDARD: Assign directly from fold() return value
  followersState.value = result.fold(
    (error) {
      _errorHandler.displayErrorToast(error, 'loadFollowers');
      return ViewModel<List<FollowerModel>>.error(error);
    },
    (followersList) {
      if (followersList.isEmpty) {
        return const ViewModel<List<FollowerModel>>.empty();
      } else {
        return ViewModel<List<FollowerModel>>.content(followersList);
      }
    },
  );
}
```

### ❌ **Avoid: Assignments Inside Callbacks**

```dart
/// Load following list - OLD PATTERN
Future<void> loadFollowing() async {
  followingState.value = const ViewModel<List<FollowerModel>>.loading();

  final result = await ServiceProvider.profileRepository.getFollowingResult(userId);

  // ❌ AVOID: Assignments inside callbacks
  result.fold(
    (error) {
      followingState.value = ViewModel<List<FollowerModel>>.error(error); // ❌
      _errorHandler.displayErrorToast(error, 'loadFollowing');
    },
    (followingList) {
      if (followingList.isEmpty) {
        followingState.value = const ViewModel<List<FollowerModel>>.empty(); // ❌
      } else {
        followingState.value = ViewModel<List<FollowerModel>>.content(followingList); // ❌
      }
    },
  );
}
```

## Enhanced Pattern with Utility Methods

### **toListViewModel() Extension Method**

For even cleaner code, use the `toListViewModel()` extension method:

```dart
/// Load followers list - ENHANCED PATTERN
Future<void> loadFollowers() async {
  followersState.value = const ViewModel<List<FollowerModel>>.loading();

  final result = await ServiceProvider.profileRepository.getFollowersResult(userId);

  // ✅ ENHANCED: Use utility extension method
  followersState.value = result.toListViewModel<FollowerModel>(
    onError: (error) => _errorHandler.displayErrorToast(error, 'loadFollowers'),
  );
}
```

### **toViewModel() Extension Method**

For non-list types:

```dart
/// Load profile data - ENHANCED PATTERN
Future<void> loadProfile() async {
  profileState.value = const ViewModel<ProfileDataModel>.loading();

  final result = await ServiceProvider.profileRepository.getProfileDataResult(username);

  // ✅ ENHANCED: Use utility extension method
  profileState.value = result.toViewModel<ProfileDataModel>(
    onError: (error) => _errorHandler.displayErrorToast(error, 'loadProfile'),
    onSuccess: (profile) => print('Profile loaded: ${profile.userName}'),
  );
}
```

## Extension Methods Implementation

### **Result Extensions**

```dart
// lib/utils/result.dart
extension ResultExtensions<T> on Result<T> {
  /// Convert Result to ViewModel with side effects
  ViewModel<T> toViewModel({
    void Function(AppError)? onError,
    void Function(T)? onSuccess,
  }) {
    return fold(
      (error) {
        onError?.call(error);
        return ViewModel<T>.error(error);
      },
      (data) {
        onSuccess?.call(data);
        if (data is List && (data as List).isEmpty) {
          return const ViewModel.empty();
        } else {
          return ViewModel<T>.content(data);
        }
      },
    );
  }

  /// Convert Result to ViewModel for List types with empty state handling
  ViewModel<List<E>> toListViewModel<E>({
    void Function(AppError)? onError,
    void Function(List<E>)? onSuccess,
  }) {
    return fold(
      (error) {
        onError?.call(error);
        return ViewModel<List<E>>.error(error);
      },
      (data) {
        onSuccess?.call(data as List<E>);
        if ((data as List<E>).isEmpty) {
          return const ViewModel<List<E>>.empty();
        } else {
          return ViewModel<List<E>>.content(data as List<E>);
        }
      },
    );
  }
}
```

## Benefits of Standard Pattern

### ✅ **Functional Programming**
- **Declarative**: State assignment is explicit and clear
- **Immutable**: No side effects inside fold callbacks
- **Composable**: Easy to chain and combine operations

### ✅ **Code Clarity**
- **Single Assignment**: One clear assignment per operation
- **Return Values**: fold() callbacks return values instead of causing side effects
- **Predictable**: Always follows the same pattern

### ✅ **Maintainability**
- **Consistent**: Same pattern across all controllers
- **Testable**: Easy to test return values
- **Debuggable**: Clear flow of data transformation

### ✅ **Performance**
- **Efficient**: No unnecessary state updates
- **Atomic**: Single state assignment per operation
- **Reactive**: UI updates once per state change

## Usage Examples

### **Basic List Loading**

```dart
class MyController extends GetxController {
  final Rx<ViewModel<List<MyModel>>> itemsState = const ViewModel<List<MyModel>>.loading().obs;

  Future<void> loadItems() async {
    itemsState.value = const ViewModel<List<MyModel>>.loading();

    final result = await repository.getItemsResult();

    itemsState.value = result.toListViewModel<MyModel>(
      onError: (error) => _errorHandler.displayErrorToast(error, 'loadItems'),
    );
  }
}
```

### **Single Item Loading**

```dart
class MyController extends GetxController {
  final Rx<ViewModel<MyModel>> itemState = const ViewModel<MyModel>.loading().obs;

  Future<void> loadItem(String id) async {
    itemState.value = const ViewModel<MyModel>.loading();

    final result = await repository.getItemResult(id);

    itemState.value = result.toViewModel<MyModel>(
      onError: (error) => _errorHandler.displayErrorToast(error, 'loadItem'),
      onSuccess: (item) => print('Loaded item: ${item.name}'),
    );
  }
}
```

### **Complex Operations with Multiple Results**

```dart
class MyController extends GetxController {
  Future<void> performComplexOperation() async {
    // Load multiple data sources
    final results = await Future.wait([
      repository.getDataAResult(),
      repository.getDataBResult(),
    ]);

    // Convert each result to ViewModel
    dataAState.value = results[0].toViewModel<DataA>(
      onError: (error) => _errorHandler.displayErrorToast(error, 'loadDataA'),
    );

    dataBState.value = results[1].toListViewModel<DataB>(
      onError: (error) => _errorHandler.displayErrorToast(error, 'loadDataB'),
    );
  }
}
```

## Side Effects Pattern

For operations that don't return ViewModel states but perform side effects:

```dart
/// Unfollow user - Side effects pattern
Future<void> unfollowUser(String username, BuildContext context) async {
  final profileResult = await repository.getProfileDataResult(username);

  await profileResult.fold(
    (error) async {
      _errorHandler.displayErrorToast(error, 'unfollowUser');
    },
    (details) async {
      if (details.userId != null) {
        final unfollowResult = await repository.unfollowUserResult(details.userId!);

        await unfollowResult.fold(
          (error) async {
            _errorHandler.displayErrorToast(error, 'unfollowUser');
          },
          (success) async {
            await refreshData();
            if (context.mounted) {
              _showSuccessSnackbar(context, details);
            }
          },
        );
      }
    },
  );
}
```

## Testing Benefits

### **Easy to Test**

```dart
test('should set content state when data loads successfully', () async {
  // Arrange
  when(mockRepository.getItemsResult())
      .thenAnswer((_) async => Right([mockItem1, mockItem2]));

  // Act
  await controller.loadItems();

  // Assert
  expect(controller.itemsState.value.state, ViewState.content);
  expect(controller.itemsState.value.data, [mockItem1, mockItem2]);
});

test('should set error state when data load fails', () async {
  // Arrange
  final error = UserError(message: 'Network error', code: 'NETWORK_ERROR');
  when(mockRepository.getItemsResult())
      .thenAnswer((_) async => Left(error));

  // Act
  await controller.loadItems();

  // Assert
  expect(controller.itemsState.value.state, ViewState.error);
  expect(controller.itemsState.value.error, error);
});
```

## Migration Checklist

- [x] ✅ **ProfileInsightsController**: Migrated to standard pattern
- [ ] 🔄 **ProfileController**: Migrate to Result-based methods
- [ ] 🔄 **Other Controllers**: Apply pattern consistently
- [ ] 🔄 **Documentation**: Update team guidelines

This standard pattern provides a clean, functional approach to error handling and state management that is consistent, testable, and maintainable across the entire codebase.
