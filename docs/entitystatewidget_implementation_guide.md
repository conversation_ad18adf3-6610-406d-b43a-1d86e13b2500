# EntityStateWidget Implementation Guide

## Overview

Complete implementation guide for EntityStateWidget - a powerful, reusable widget that provides consistent state rendering (loading, content, empty, error) with enhanced error handling and debug capabilities.

## Core Implementation

### **EntityStateWidget Structure**

```dart
class EntityStateWidget<T> extends StatelessWidget {
  final ViewModel<T> model;
  final Widget Function(T data) itemBuilder;
  final VoidCallback? onRetry;
  final Widget? loadingWidget;
  final Widget? emptyWidget;
  final Widget? errorWidget;
  final EdgeInsets? padding;
  final bool showDebugInfo;

  const EntityStateWidget({
    Key? key,
    required this.model,
    required this.itemBuilder,
    this.onRetry,
    this.loadingWidget,
    this.emptyWidget,
    this.errorWidget,
    this.padding,
    this.showDebugInfo = kDebugMode,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Widget child;
    
    switch (model.state) {
      case ViewState.loading:
        child = loadingWidget ?? const DefaultLoadingWidget();
        break;
      case ViewState.content:
        child = itemBuilder(model.data!);
        break;
      case ViewState.empty:
        child = emptyWidget ?? DefaultEmptyWidget(onRetry: onRetry);
        break;
      case ViewState.error:
        child = errorWidget ?? DefaultErrorWidget(
          error: model.error,
          onRetry: onRetry,
          showDebugInfo: showDebugInfo,
        );
        break;
    }
    
    return padding != null ? Padding(padding: padding!, child: child) : child;
  }
}
```

## Enhanced Error Widget

### **DefaultErrorWidget with Dynamic Error Handling**

```dart
class DefaultErrorWidget extends StatefulWidget {
  final dynamic error;
  final VoidCallback? onRetry;
  final bool showDebugInfo;

  const DefaultErrorWidget({
    Key? key,
    required this.error,
    this.onRetry,
    this.showDebugInfo = kDebugMode,
  }) : super(key: key);

  @override
  State<DefaultErrorWidget> createState() => _DefaultErrorWidgetState();
}

class _DefaultErrorWidgetState extends State<DefaultErrorWidget> {
  bool _showDebugDetails = false;

  @override
  Widget build(BuildContext context) {
    final isAppError = widget.error is AppError;
    final appError = isAppError ? widget.error as AppError : null;
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Error icon with severity-based styling
            Icon(
              _getErrorIcon(appError?.severity),
              size: 64,
              color: _getErrorColor(appError?.severity),
            ),
            
            const SizedBox(height: 16),
            
            // User-friendly error message
            Text(
              isAppError ? appError!.message : widget.error.toString(),
              style: Theme.of(context).textTheme.titleMedium,
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 24),
            
            // Retry button
            if (widget.onRetry != null)
              ElevatedButton.icon(
                onPressed: widget.onRetry,
                icon: const Icon(Icons.refresh),
                label: const Text('Retry'),
              ),
            
            // Debug information (development only)
            if (widget.showDebugInfo && isAppError) ...[
              const SizedBox(height: 16),
              _buildDebugSection(appError!),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDebugSection(AppError error) {
    return Card(
      color: Colors.grey[100],
      child: ExpansionTile(
        title: Row(
          children: [
            Icon(Icons.bug_report, size: 16, color: Colors.orange[700]),
            const SizedBox(width: 8),
            const Text('Debug Info', style: TextStyle(fontSize: 14)),
          ],
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildDebugRow('Error Code', error.code),
                _buildDebugRow('Severity', error.severity.toString()),
                if (error.originalError != null)
                  _buildDebugRow('Original Error', error.originalError.toString()),
                if (error.metadata != null) ...[
                  const SizedBox(height: 8),
                  const Text('Metadata:', style: TextStyle(fontWeight: FontWeight.bold)),
                  ...error.metadata!.entries.map((entry) =>
                    _buildDebugRow(entry.key, entry.value.toString())),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDebugRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text('$label:', style: const TextStyle(fontWeight: FontWeight.w500)),
          ),
          Expanded(
            child: Text(value, style: const TextStyle(fontFamily: 'monospace')),
          ),
        ],
      ),
    );
  }

  IconData _getErrorIcon(ErrorSeverity? severity) {
    switch (severity) {
      case ErrorSeverity.fatal:
        return Icons.error;
      case ErrorSeverity.unexpected:
        return Icons.warning;
      case ErrorSeverity.warning:
        return Icons.info_outline;
      case ErrorSeverity.expected:
      default:
        return Icons.error_outline;
    }
  }

  Color _getErrorColor(ErrorSeverity? severity) {
    switch (severity) {
      case ErrorSeverity.fatal:
        return Colors.red[700]!;
      case ErrorSeverity.unexpected:
        return Colors.orange[700]!;
      case ErrorSeverity.warning:
        return Colors.blue[700]!;
      case ErrorSeverity.expected:
      default:
        return Colors.grey[600]!;
    }
  }
}
```

## Default State Widgets

### **DefaultLoadingWidget**

```dart
class DefaultLoadingWidget extends StatelessWidget {
  final String? message;
  
  const DefaultLoadingWidget({Key? key, this.message}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(),
          if (message != null) ...[
            const SizedBox(height: 16),
            Text(message!, style: Theme.of(context).textTheme.bodyMedium),
          ],
        ],
      ),
    );
  }
}
```

### **DefaultEmptyWidget**

```dart
class DefaultEmptyWidget extends StatelessWidget {
  final String? message;
  final String? actionText;
  final VoidCallback? onRetry;
  final IconData? icon;
  
  const DefaultEmptyWidget({
    Key? key,
    this.message,
    this.actionText,
    this.onRetry,
    this.icon,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon ?? Icons.inbox_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              message ?? 'No data available',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            if (onRetry != null) ...[
              const SizedBox(height: 24),
              OutlinedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh),
                label: Text(actionText ?? 'Refresh'),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
```

## Usage Patterns

### **Basic Usage**

```dart
class ProductsPage extends StatelessWidget {
  final ProductsController controller = Get.find<ProductsController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Products')),
      body: Obx(() => EntityStateWidget<List<Product>>(
        model: controller.productsState,
        onRetry: () => controller.loadProducts(),
        itemBuilder: (products) => ListView.builder(
          itemCount: products.length,
          itemBuilder: (context, index) => ProductCard(products[index]),
        ),
      )),
    );
  }
}
```

### **Custom State Widgets**

```dart
EntityStateWidget<List<Product>>(
  model: controller.productsState,
  onRetry: () => controller.loadProducts(),
  itemBuilder: (products) => ProductGrid(products: products),
  
  // Custom loading with brand styling
  loadingWidget: const CustomLoadingWidget(
    message: 'Loading awesome products...',
    color: AppColors.primary,
  ),
  
  // Custom empty state with call-to-action
  emptyWidget: EmptyProductsWidget(
    onAddProduct: () => controller.navigateToAddProduct(),
    onBrowseCategories: () => controller.navigateToCategories(),
  ),
  
  // Custom error with additional actions
  errorWidget: CustomErrorWidget(
    onRetry: () => controller.loadProducts(),
    onContactSupport: () => controller.contactSupport(),
    onGoOffline: () => controller.enableOfflineMode(),
  ),
)
```

### **Nested EntityStateWidgets**

```dart
class ProfilePage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // Profile header section
          Obx(() => EntityStateWidget<ProfileModel>(
            model: controller.profileState,
            onRetry: () => controller.loadProfile(),
            itemBuilder: (profile) => ProfileHeader(profile: profile),
            loadingWidget: const ProfileHeaderSkeleton(),
          )),
          
          // Tabs section
          const ProfileTabs(),
          
          // Content section
          Expanded(
            child: TabBarView(
              children: [
                // Posts tab
                Obx(() => EntityStateWidget<List<PostModel>>(
                  model: controller.postsState,
                  onRetry: () => controller.loadPosts(),
                  itemBuilder: (posts) => PostsGrid(posts: posts),
                  emptyWidget: const EmptyPostsWidget(),
                )),
                
                // Followers tab
                Obx(() => EntityStateWidget<List<FollowerModel>>(
                  model: controller.followersState,
                  onRetry: () => controller.loadFollowers(),
                  itemBuilder: (followers) => FollowersList(followers: followers),
                  emptyWidget: const EmptyFollowersWidget(),
                )),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
```

## Advanced Features

### **Conditional Rendering**

```dart
EntityStateWidget<UserProfile>(
  model: controller.profileState,
  onRetry: () => controller.loadProfile(),
  itemBuilder: (profile) {
    // Conditional rendering based on data
    if (profile.isPrivate && !profile.isFollowing) {
      return const PrivateProfileWidget();
    }
    return PublicProfileWidget(profile: profile);
  },
)
```

### **Loading States with Progress**

```dart
class ProgressLoadingWidget extends StatelessWidget {
  final double? progress;
  final String? message;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (progress != null)
            CircularProgressIndicator(value: progress)
          else
            const CircularProgressIndicator(),
          
          if (message != null) ...[
            const SizedBox(height: 16),
            Text(message!),
          ],
          
          if (progress != null) ...[
            const SizedBox(height: 8),
            Text('${(progress! * 100).toInt()}%'),
          ],
        ],
      ),
    );
  }
}
```

### **Error Recovery Strategies**

```dart
class SmartErrorWidget extends StatelessWidget {
  final AppError error;
  final VoidCallback? onRetry;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        DefaultErrorWidget(error: error, onRetry: onRetry),
        
        // Additional recovery options based on error type
        if (error is NetworkError) ...[
          const SizedBox(height: 16),
          OutlinedButton(
            onPressed: () => _enableOfflineMode(),
            child: const Text('Browse Offline'),
          ),
        ],
        
        if (error.severity == ErrorSeverity.fatal) ...[
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => _restartApp(),
            child: const Text('Restart App'),
          ),
        ],
      ],
    );
  }
}
```

## Testing EntityStateWidget

### **Widget Tests**

```dart
group('EntityStateWidget Tests', () {
  testWidgets('should display loading widget when state is loading', (tester) async {
    const model = ViewModel<String>.loading();
    
    await tester.pumpWidget(MaterialApp(
      home: EntityStateWidget<String>(
        model: model,
        itemBuilder: (data) => Text(data),
      ),
    ));
    
    expect(find.byType(CircularProgressIndicator), findsOneWidget);
  });

  testWidgets('should display content when state is content', (tester) async {
    const model = ViewModel<String>.content('Hello World');
    
    await tester.pumpWidget(MaterialApp(
      home: EntityStateWidget<String>(
        model: model,
        itemBuilder: (data) => Text(data),
      ),
    ));
    
    expect(find.text('Hello World'), findsOneWidget);
  });

  testWidgets('should display error widget when state is error', (tester) async {
    final error = UserError(message: 'Test error', code: 'TEST_ERROR');
    final model = ViewModel<String>.error(error);
    
    await tester.pumpWidget(MaterialApp(
      home: EntityStateWidget<String>(
        model: model,
        itemBuilder: (data) => Text(data),
        onRetry: () {},
      ),
    ));
    
    expect(find.text('Test error'), findsOneWidget);
    expect(find.text('Retry'), findsOneWidget);
  });

  testWidgets('should call onRetry when retry button is pressed', (tester) async {
    bool retryPressed = false;
    final model = ViewModel<String>.error('Test error');
    
    await tester.pumpWidget(MaterialApp(
      home: EntityStateWidget<String>(
        model: model,
        itemBuilder: (data) => Text(data),
        onRetry: () => retryPressed = true,
      ),
    ));
    
    await tester.tap(find.text('Retry'));
    expect(retryPressed, isTrue);
  });
});
```

## Performance Optimization

### **Efficient Rebuilds**
- Use `const` constructors where possible
- Wrap only necessary widgets with `Obx()`
- Consider using `GetBuilder` for complex widgets that don't need reactive updates

### **Memory Management**
- Dispose of large data sets when not needed
- Use lazy loading for large lists
- Implement proper controller lifecycle management

## Related Documentation

- **[Error Handling Architecture](./error_handling_architecture.md)** - Complete error handling system
- **[State Management with ViewModel](./state_management_viewmodel.md)** - ViewModel patterns and controller architecture

EntityStateWidget provides a powerful, consistent foundation for state rendering that improves user experience, reduces boilerplate code, and ensures consistent error handling across your Flutter application.
