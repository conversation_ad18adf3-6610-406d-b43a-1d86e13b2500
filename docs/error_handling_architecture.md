# Error Handling Architecture

## Overview

Comprehensive error handling architecture using Result types, AppError hierarchy, and functional programming patterns for consistent, maintainable error management across the Flutter application.

## Architecture Components

### 1. **Error Models Hierarchy**

```dart
// Base error class
abstract class AppError {
  final String message;
  final String code;
  final dynamic originalError;
  final Map<String, dynamic>? metadata;
  final ErrorSeverity severity;
  
  const AppError({
    required this.message,
    required this.code,
    this.originalError,
    this.metadata,
    this.severity = ErrorSeverity.expected,
  });
}

// Specific error types
class UserError extends AppError { /* User-facing errors */ }
class ValidationError extends AppError { /* Input validation errors */ }
class NetworkError extends AppError { /* Network-related errors */ }
class ServerError extends AppError { /* Server response errors */ }
class UnexpectedError extends AppError { /* Unexpected system errors */ }
```

### 2. **Error Severity Levels**

```dart
enum ErrorSeverity {
  expected,    // Normal business logic errors (validation, not found)
  warning,     // Recoverable issues (network timeout, retry possible)
  unexpected,  // Unexpected but handled errors (parsing failures)
  fatal,       // Critical system errors (out of memory, security)
}
```

### 3. **Result Type System**

```dart
// Type alias for Either from dartz package
typedef Result<T> = Either<AppError, T>;

// Extension methods for Result
extension ResultExtensions<T> on Result<T> {
  // Convert to ViewModel with side effects
  ViewModel<T> toViewModel({
    void Function(AppError)? onError,
    void Function(T)? onSuccess,
  });
  
  // Convert to ViewModel for List types
  ViewModel<List<E>> toListViewModel<E>({
    void Function(AppError)? onError,
    void Function(List<E>)? onSuccess,
  });
  
  // Execute side effects
  Result<T> onError(void Function(AppError) action);
  Result<T> onSuccess(void Function(T) action);
}
```

## Error Handling Layers

### **Repository Layer**
- **Responsibility**: Catch and format errors using global infrastructure
- **Pattern**: Use `ResultHelper.tryCallAsync()` for consistent error wrapping
- **Output**: Return `Result<T>` types instead of throwing exceptions

```dart
class ProfileRepository {
  Future<Result<ProfileDataModel>> getProfileDataResult(String username) async {
    return ResultHelper.tryCallAsync(
      () async {
        final response = await _dioService.get(ApiPaths.userProfile(username));
        return ProfileDataModel.fromJson(response.data);
      },
      errorMessage: 'Unable to load profile data. Please try again.',
      errorCode: 'PROFILE_LOAD_ERROR',
      metadata: {
        'username': username,
        'operation': 'getProfileData',
      },
    );
  }
}
```

### **Controller Layer**
- **Responsibility**: Handle business logic and state management
- **Pattern**: Use functional `result.fold()` or extension methods
- **Output**: Update reactive state variables

```dart
class ProfileController extends GetxController {
  final Rx<ViewModel<ProfileDataModel>> profileState = 
      const ViewModel<ProfileDataModel>.loading().obs;

  Future<void> loadProfile() async {
    profileState.value = const ViewModel<ProfileDataModel>.loading();
    
    final result = await ServiceProvider.profileRepository.getProfileDataResult(username);
    
    // Standard pattern: direct assignment from fold
    profileState.value = result.toViewModel<ProfileDataModel>(
      onError: (error) => _errorHandler.displayErrorToast(error, 'loadProfile'),
    );
  }
}
```

### **UI Layer**
- **Responsibility**: Display states and handle user interactions
- **Pattern**: Use `EntityStateWidget` for consistent state rendering
- **Output**: Reactive UI that responds to state changes

```dart
class ProfilePage extends StatelessWidget {
  Widget build(BuildContext context) {
    return Obx(() => EntityStateWidget<ProfileDataModel>(
      model: controller.profileState,
      onRetry: () => controller.loadProfile(),
      itemBuilder: (profile) => ProfileView(profile: profile),
    ));
  }
}
```

## Error Propagation Flow

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Repository    │    │   Controller     │    │       UI        │
│                 │    │                  │    │                 │
│ HTTP Error      │───▶│ Result<T>        │───▶│ EntityStateWidget│
│ Parse Error     │    │ .fold()          │    │ Error Display   │
│ Network Error   │    │ .toViewModel()   │    │ Retry Button    │
│                 │    │                  │    │                 │
│ ↓ Wrap in       │    │ ↓ Convert to     │    │ ↓ Render        │
│ AppError        │    │ ViewModel        │    │ Error State     │
│ ↓ Add Context   │    │ ↓ Update State   │    │ ↓ Show Debug    │
│ ↓ Return Result │    │ ↓ Handle Effects │    │ Info (dev mode) │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## Error Context Enhancement

### **Automatic Context Addition**

```dart
class ResultHelper {
  static Future<Result<T>> tryCallAsync<T>(
    Future<T> Function() operation, {
    required String errorMessage,
    required String errorCode,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final result = await operation();
      return Right(result);
    } catch (e) {
      // Enhanced error context
      final enhancedMetadata = {
        ...?metadata,
        'timestamp': DateTime.now().toIso8601String(),
        'operation': errorCode,
        'stackTrace': StackTrace.current.toString(),
      };
      
      if (e is AppError) {
        return Left(e.copyWith(metadata: enhancedMetadata));
      }
      
      return Left(UserError(
        message: errorMessage,
        code: errorCode,
        originalError: e,
        metadata: enhancedMetadata,
      ));
    }
  }
}
```

## Global Error Reporting

### **Error Reporting Service Integration**

```dart
class ErrorHandler {
  void displayErrorToast(dynamic error, String operation) {
    if (error is AppError) {
      // Report to analytics/crash reporting
      ErrorReportingService.reportError(
        error: error,
        operation: operation,
        severity: error.severity,
      );
      
      // Show user-friendly message
      _showToast(error.message);
      
      // Log detailed info in debug mode
      if (kDebugMode) {
        debugPrint('Error in $operation: ${error.toDebugString()}');
      }
    } else {
      // Handle legacy string errors
      _showToast(error.toString());
    }
  }
}
```

## Migration Strategy

### **Phase 1: Repository Layer** ✅
- [x] Create Result-based repository methods
- [x] Implement LegacyRepository for backward compatibility
- [x] Add ResultHelper utilities

### **Phase 2: Controller Layer** 🔄
- [x] ProfileInsightsController (Complete)
- [ ] ProfileController (In Progress)
- [ ] Other controllers (Planned)

### **Phase 3: UI Layer** ✅
- [x] EntityStateWidget implementation
- [x] Dynamic error handling
- [x] Debug mode enhancements

### **Phase 4: Cleanup** 🎯
- [ ] Remove legacy repositories
- [ ] Update all controllers
- [ ] Comprehensive testing

## Best Practices

### ✅ **Do**
- Use Result types for all async operations
- Implement proper error context and metadata
- Use functional patterns (fold, extension methods)
- Provide user-friendly error messages
- Include debug information in development

### ❌ **Don't**
- Throw exceptions in repository layer
- Use try-catch in controllers (use Result.fold instead)
- Assign state inside fold callbacks
- Show technical error messages to users
- Ignore error reporting and analytics

## Related Documentation

- **[State Management with ViewModel](./state_management_viewmodel.md)** - ViewModel patterns and EntityStateWidget usage
- **[Result to ViewModel Patterns](./result_to_viewmodel_standard_pattern.md)** - Standard functional patterns for error handling

## Testing Error Handling

```dart
group('Error Handling', () {
  test('should handle network errors gracefully', () async {
    // Arrange
    when(mockRepository.getDataResult())
        .thenAnswer((_) async => Left(NetworkError(
          message: 'Network connection failed',
          code: 'NETWORK_ERROR',
        )));

    // Act
    await controller.loadData();

    // Assert
    expect(controller.dataState.value.state, ViewState.error);
    expect(controller.dataState.value.error, isA<NetworkError>());
    verify(mockErrorHandler.displayErrorToast(any, 'loadData')).called(1);
  });
});
```

This architecture provides a robust, scalable foundation for error handling that improves user experience, developer productivity, and application reliability.
