# EntityStateWidget Usage Example - ProfileInsightsPage

## Overview

This document demonstrates how EntityStateWidget is used in ProfileInsightsPage to provide consistent state management across different UI states.

## State Flow Diagram

```
┌─────────────────┐
│   Initial Load  │
│   (Loading)     │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API Success   │    │   API Failure   │    │  Empty Response │
│   (Content)     │    │   (Error)       │    │   (Empty)       │
└─────────────────┘    └─────────┬───────┘    └─────────────────┘
                                 │
                                 ▼
                       ┌─────────────────┐
                       │  User Taps      │
                       │  Retry Button   │
                       │  (Loading)      │
                       └─────────────────┘
```

## Implementation Example

### 1. **Controller State Management**

```dart
class ProfileInsightsController extends GetxController {
  // ViewModel states for each tab
  final Rx<ViewModel<List<FollowerModel>>> followersState = 
      const ViewModel<List<FollowerModel>>.loading().obs;
  final Rx<ViewModel<List<FollowerModel>>> followingState = 
      const ViewModel<List<FollowerModel>>.loading().obs;

  // Load followers with proper state transitions
  Future<void> loadFollowers() async {
    try {
      // Set loading state
      followersState.value = const ViewModel<List<FollowerModel>>.loading();
      
      // Fetch data from repository
      final followersList = await ServiceProvider.profileRepository.getFollowers(userId);
      
      // Set appropriate state based on data
      if (followersList.isEmpty) {
        followersState.value = const ViewModel<List<FollowerModel>>.empty();
      } else {
        followersState.value = ViewModel<List<FollowerModel>>.content(followersList);
      }
    } catch (e) {
      // Set error state with user-friendly message
      final errorMessage = _getErrorMessage(e);
      followersState.value = ViewModel<List<FollowerModel>>.error(errorMessage);
      
      // Still show toast for debugging (optional)
      _errorHandler.displayErrorToast(e, 'loadFollowers');
    }
  }
}
```

### 2. **UI Implementation with EntityStateWidget**

```dart
class ProfileInsightsPage extends GetView<ProfileInsightsController> {
  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      initialIndex: initialTab,
      length: 2,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Profile Insights'),
          bottom: const TabBar(
            tabs: [
              Tab(text: 'Followers'),
              Tab(text: 'Following'),
            ],
          ),
        ),
        body: Container(
          color: Colors.white,
          child: TabBarView(
            children: [
              // Followers Tab with EntityStateWidget
              EntityStateWidget<List<FollowerModel>>(
                model: controller.followersState,
                onRetry: () => controller.loadFollowers(),
                emptyMessage: "No Followers yet",
                emptyIcon: const Icon(
                  Icons.people_outline,
                  size: 64,
                  color: Colors.grey,
                ),
                itemBuilder: (followers) => _buildFollowersGrid(followers),
              ),
              
              // Following Tab with EntityStateWidget
              EntityStateWidget<List<FollowerModel>>(
                model: controller.followingState,
                onRetry: () => controller.loadFollowing(),
                emptyMessage: "User not following anyone.",
                emptyIcon: const Icon(
                  Icons.person_add_outlined,
                  size: 64,
                  color: Colors.grey,
                ),
                itemBuilder: (following) => _buildFollowingGrid(following),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
```

## State Scenarios

### 1. **Loading State**
```dart
// Controller sets loading state
followersState.value = const ViewModel<List<FollowerModel>>.loading();

// EntityStateWidget automatically shows:
// - CircularProgressIndicator (default)
// - Or custom loading widget if provided
```

**UI Result:**
```
┌─────────────────────────────┐
│                             │
│    ⟳ CircularProgressIndicator │
│                             │
│      Loading followers...   │
│                             │
└─────────────────────────────┘
```

### 2. **Content State**
```dart
// Controller sets content state with data
followersState.value = ViewModel<List<FollowerModel>>.content(followersList);

// EntityStateWidget calls itemBuilder with the data
itemBuilder: (followers) => _buildFollowersGrid(followers)
```

**UI Result:**
```
┌─────────────────────────────┐
│  ┌─────────┐  ┌─────────┐   │
│  │ User 1  │  │ User 2  │   │
│  │ Avatar  │  │ Avatar  │   │
│  │ @user1  │  │ @user2  │   │
│  └─────────┘  └─────────┘   │
│  ┌─────────┐  ┌─────────┐   │
│  │ User 3  │  │ User 4  │   │
│  │ Avatar  │  │ Avatar  │   │
│  │ @user3  │  │ @user4  │   │
│  └─────────┘  └─────────┘   │
└─────────────────────────────┘
```

### 3. **Empty State**
```dart
// Controller sets empty state
followersState.value = const ViewModel<List<FollowerModel>>.empty();

// EntityStateWidget shows custom empty message and icon
```

**UI Result:**
```
┌─────────────────────────────┐
│                             │
│        👥                   │
│     (64px icon)             │
│                             │
│    No Followers yet         │
│                             │
└─────────────────────────────┘
```

### 4. **Error State**
```dart
// Controller sets error state with message
followersState.value = ViewModel<List<FollowerModel>>.error("Network error");

// EntityStateWidget shows error message with retry button
```

**UI Result:**
```
┌─────────────────────────────┐
│                             │
│        ⚠️                   │
│     (64px icon)             │
│                             │
│  Network error. Please      │
│  check your connection.     │
│                             │
│    ┌─────────────┐          │
│    │   🔄 Retry  │          │
│    └─────────────┘          │
└─────────────────────────────┘
```

## Custom Builders

### **Custom Loading Widget**
```dart
EntityStateWidget<List<FollowerModel>>(
  model: controller.followersState,
  loadingBuilder: () => Column(
    mainAxisAlignment: MainAxisAlignment.center,
    children: [
      const CircularProgressIndicator(color: Colors.blue),
      const SizedBox(height: 16),
      Text(
        'Loading followers...',
        style: TextStyle(color: Colors.grey[600]),
      ),
    ],
  ),
  itemBuilder: (followers) => _buildFollowersGrid(followers),
)
```

### **Custom Error Widget**
```dart
EntityStateWidget<List<FollowerModel>>(
  model: controller.followersState,
  errorBuilder: (message, onRetry) => Container(
    padding: const EdgeInsets.all(32),
    child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(Icons.wifi_off, size: 64, color: Colors.red[300]),
        const SizedBox(height: 16),
        Text(
          message ?? 'Something went wrong',
          style: const TextStyle(fontSize: 16, color: Colors.red),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 24),
        ElevatedButton.icon(
          onPressed: onRetry,
          icon: const Icon(Icons.refresh),
          label: const Text('Try Again'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue,
            foregroundColor: Colors.white,
          ),
        ),
      ],
    ),
  ),
  itemBuilder: (followers) => _buildFollowersGrid(followers),
)
```

## Error Handling Flow

### **Repository Level**
```dart
// Repository propagates errors naturally
Future<List<FollowerModel>> getFollowers(String userId) async {
  final response = await _dioService.get(ApiPaths.getFollowers(userId));
  // Parse and return data
  // Let any exceptions bubble up to controller
}
```

### **Controller Level**
```dart
// Controller catches and transforms errors
Future<void> loadFollowers() async {
  try {
    followersState.value = const ViewModel.loading();
    final data = await repository.getFollowers(userId);
    followersState.value = ViewModel.content(data);
  } catch (e) {
    // Transform technical error to user-friendly message
    final userMessage = _getErrorMessage(e);
    followersState.value = ViewModel.error(userMessage);
  }
}

String _getErrorMessage(dynamic error) {
  if (error.toString().contains('404')) return 'User not found';
  if (error.toString().contains('timeout')) return 'Connection timeout';
  return 'Something went wrong. Please try again.';
}
```

### **UI Level**
```dart
// EntityStateWidget handles display automatically
EntityStateWidget<List<FollowerModel>>(
  model: controller.followersState,
  onRetry: () => controller.loadFollowers(), // Retry calls controller method
  itemBuilder: (data) => _buildContent(data),
)
```

## Benefits

### ✅ **Consistency**
- All states look the same across the app
- Standardized error messages and retry mechanisms
- Consistent loading indicators

### ✅ **Maintainability**
- Single place to update state UI
- Clear separation of concerns
- Easy to test each state independently

### ✅ **User Experience**
- Professional loading states
- Clear error messages with recovery options
- Informative empty states

### ✅ **Developer Experience**
- Less boilerplate code
- Automatic state management
- Type-safe state transitions

This implementation provides a robust foundation for state management that can be easily replicated across other pages in the application.
