# State Management with ViewModel

## Quick Start Guide

This guide helps newcomers understand our state management system and start using it effectively in 5 minutes.

## What is ViewModel?

**ViewModel** is a simple wrapper that holds your data and its current state (loading, loaded, empty, or error). Think of it as a smart container that tells your UI exactly what to display.

### **The 4 States Every Screen Has**

1. **Loading** - "Please wait, fetching data..."
2. **Content** - "Here's your data!"
3. **Empty** - "No data found"
4. **Error** - "Something went wrong"

### **How to Use ViewModel (3 Steps)**

**Step 1: Declare your state variable**
```dart
final Rx<ViewModel<List<Product>>> productsState = ViewModel<List<Product>>.loading().obs;
```

**Step 2: Update state when loading data**
```dart
// Set loading
productsState.value = ViewModel<List<Product>>.loading();

// Set content when data arrives
productsState.value = ViewModel<List<Product>>.content(productList);

// Set error if something fails
productsState.value = ViewModel<List<Product>>.error("Failed to load products");
```

**Step 3: Display in UI**
```dart
Obx(() => EntityStateWidget<List<Product>>(
  model: controller.productsState,
  onRetry: () => controller.loadProducts(),
  itemBuilder: (products) => ProductList(products: products),
))
```

## Essential Functions You'll Use Daily

### **For Controllers**

| Function | When to Use | Example |
|----------|-------------|---------|
| `ViewModel.loading()` | Starting any data fetch | `state.value = ViewModel.loading()` |
| `ViewModel.content(data)` | When data loads successfully | `state.value = ViewModel.content(users)` |
| `ViewModel.empty()` | When no data is found | `state.value = ViewModel.empty()` |
| `ViewModel.error(error)` | When something goes wrong | `state.value = ViewModel.error("Network error")` |
| `result.toListViewModel()` | Convert API result to ViewModel | `state.value = result.toListViewModel()` |

### **For UI Widgets**

| Widget | Purpose | When to Use |
|--------|---------|-------------|
| `EntityStateWidget` | Automatically handles all 4 states | Every screen that loads data |
| `Obx(() => ...)` | Makes widget reactive to state changes | Wrap around EntityStateWidget |
| `onRetry: () => controller.reload()` | Retry button for errors | Always provide retry functionality |

## Learn from Working Examples

### **Perfect Implementation Examples**

| Example Class | What It Shows | Key Learning |
|---------------|---------------|--------------|
| `ProfileInsightsController` | Complete Result-based pattern | Copy the `loadFollowers()` and `loadFollowing()` methods |
| `ProfileInsightsPage` | EntityStateWidget usage | See how `Obx()` wraps EntityStateWidget |
| `DefaultErrorWidget` | Smart error display | Handles both string and AppError types |
| `ProfileRepository` | Modern Result-based methods | All methods end with "Result" suffix |
| `LegacyProfileRepository` | Old exception-based methods | What NOT to copy for new code |

### **Quick Copy-Paste Patterns**

**For Controllers (copy from ProfileInsightsController):**
- `loadFollowers()` method → Pattern for loading lists
- `loadFollowing()` method → Same pattern, different data
- `unfollowUser()` method → Pattern for actions with side effects

**For UI (copy from ProfileInsightsPage):**
- `EntityStateWidget` usage → How to display any data type
- `Obx()` wrapping → Making widgets reactive
- `onRetry` callbacks → Providing retry functionality

## Quick Reference: What Each File Does

| File Location | What It Contains | When You Need It |
|---------------|------------------|------------------|
| `lib/ui/core/entity_state_widget.dart` | The magic widget that handles all states | Every screen that loads data |
| `lib/utils/result.dart` | Helper functions like `toListViewModel()` | When converting API results |
| `lib/services/error/error_models.dart` | Error types and severity levels | When handling specific errors |
| `lib/ui/profile/insights/profile_insights_controller.dart` | Example of perfect implementation | Copy this pattern for new controllers |

## Troubleshooting Guide

### **Common Issues & Solutions**

| Problem | Solution | Example |
|---------|----------|---------|
| "UI not updating" | Wrap with `Obx()` | `Obx(() => EntityStateWidget(...))` |
| "Always shows loading" | Check if you're setting content state | `state.value = ViewModel.content(data)` |
| "Error not displaying" | Ensure onRetry is provided | `onRetry: () => controller.reload()` |
| "Empty state not showing" | Use `toListViewModel()` for lists | `result.toListViewModel<Item>()` |

### **Quick Debugging**

**Check your state in debug console:**
```dart
print('Current state: ${controller.itemsState.value.state}');
print('Has data: ${controller.itemsState.value.data != null}');
print('Error: ${controller.itemsState.value.error}');
```

**Force a specific state for testing:**
```dart
// Test loading state
controller.itemsState.value = ViewModel<List<Item>>.loading();

// Test error state
controller.itemsState.value = ViewModel<List<Item>>.error("Test error");

// Test empty state
controller.itemsState.value = ViewModel<List<Item>>.empty();
```

## Migration from Old Code

### **If you see try-catch blocks:**
```dart
// OLD WAY ❌
try {
  final data = await repository.getData();
  state.value = ViewModel.content(data);
} catch (e) {
  state.value = ViewModel.error(e.toString());
}

// NEW WAY ✅
final result = await repository.getDataResult();
state.value = result.toListViewModel<Item>(
  onError: (error) => _errorHandler.displayErrorToast(error, 'loadData'),
);
```

### **If you see manual state assignments:**
```dart
// OLD WAY ❌
result.fold(
  (error) {
    state.value = ViewModel.error(error);  // Assignment inside callback
  },
  (data) {
    state.value = ViewModel.content(data); // Assignment inside callback
  },
);

// NEW WAY ✅
state.value = result.fold(
  (error) => ViewModel.error(error),      // Return from callback
  (data) => ViewModel.content(data),      // Return from callback
);
```

## UI Integration Patterns

### **Basic Usage**

```dart
class MyPage extends StatelessWidget {
  final MyController controller = Get.find<MyController>();

  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(() => EntityStateWidget<List<MyModel>>(
        model: controller.itemsState,
        onRetry: () => controller.loadItems(),
        itemBuilder: (items) => ListView.builder(
          itemCount: items.length,
          itemBuilder: (context, index) => MyItemWidget(items[index]),
        ),
      )),
    );
  }
}
```

### **Custom State Widgets**

```dart
EntityStateWidget<List<Product>>(
  model: controller.productsState,
  onRetry: () => controller.loadProducts(),
  itemBuilder: (products) => ProductGrid(products: products),
  loadingWidget: ProductGridSkeleton(),
  emptyWidget: EmptyProductsWidget(
    onAddProduct: () => controller.navigateToAddProduct(),
  ),
  errorWidget: CustomErrorWidget(
    onRetry: () => controller.loadProducts(),
    onContactSupport: () => controller.contactSupport(),
  ),
)
```

### **Nested State Management**

```dart
class ProfilePage extends StatelessWidget {
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Profile header
        Obx(() => EntityStateWidget<ProfileModel>(
          model: controller.profileState,
          onRetry: () => controller.loadProfile(),
          itemBuilder: (profile) => ProfileHeader(profile: profile),
        )),
        
        // Posts section
        Expanded(
          child: Obx(() => EntityStateWidget<List<PostModel>>(
            model: controller.postsState,
            onRetry: () => controller.loadPosts(),
            itemBuilder: (posts) => PostsList(posts: posts),
          )),
        ),
      ],
    );
  }
}
```

## Advanced Patterns

### **Conditional State Updates**

```dart
Future<void> loadItemsIfNeeded() async {
  // Only load if not already loaded or in error state
  if (itemsState.value.state == ViewState.content) {
    return; // Already loaded
  }
  
  await loadItems();
}
```

### **Optimistic Updates**

```dart
Future<void> toggleFavorite(String itemId) async {
  // Optimistically update UI
  final currentItems = itemsState.value.data ?? [];
  final updatedItems = currentItems.map((item) {
    if (item.id == itemId) {
      return item.copyWith(isFavorite: !item.isFavorite);
    }
    return item;
  }).toList();
  
  itemsState.value = ViewModel<List<MyModel>>.content(updatedItems);
  
  // Perform actual update
  final result = await repository.toggleFavoriteResult(itemId);
  
  result.fold(
    (error) {
      // Revert on error
      itemsState.value = ViewModel<List<MyModel>>.content(currentItems);
      _errorHandler.displayErrorToast(error, 'toggleFavorite');
    },
    (updatedItem) {
      // Update with server response
      final finalItems = currentItems.map((item) {
        return item.id == itemId ? updatedItem : item;
      }).toList();
      itemsState.value = ViewModel<List<MyModel>>.content(finalItems);
    },
  );
}
```

### **Pagination Support**

```dart
class PaginatedController extends GetxController {
  final Rx<ViewModel<List<MyModel>>> itemsState = 
      const ViewModel<List<MyModel>>.loading().obs;
  
  final RxBool isLoadingMore = false.obs;
  int _currentPage = 1;
  bool _hasMoreData = true;

  Future<void> loadItems({bool isRefresh = false}) async {
    if (isRefresh) {
      _currentPage = 1;
      _hasMoreData = true;
      itemsState.value = const ViewModel<List<MyModel>>.loading();
    }
    
    final result = await repository.getItemsResult(page: _currentPage);
    
    result.fold(
      (error) {
        if (isRefresh) {
          itemsState.value = ViewModel<List<MyModel>>.error(error);
        }
        _errorHandler.displayErrorToast(error, 'loadItems');
      },
      (newItems) {
        final currentItems = isRefresh ? <MyModel>[] : (itemsState.value.data ?? <MyModel>[]);
        final allItems = [...currentItems, ...newItems];
        
        _hasMoreData = newItems.isNotEmpty;
        _currentPage++;
        
        itemsState.value = allItems.isEmpty 
            ? const ViewModel<List<MyModel>>.empty()
            : ViewModel<List<MyModel>>.content(allItems);
      },
    );
  }

  Future<void> loadMore() async {
    if (!_hasMoreData || isLoadingMore.value) return;
    
    isLoadingMore.value = true;
    await loadItems();
    isLoadingMore.value = false;
  }
}
```

## Testing Patterns

### **State Testing**

```dart
group('ViewModel State Management', () {
  test('should start with loading state', () {
    expect(controller.itemsState.value.state, ViewState.loading);
  });

  test('should set content state when data loads successfully', () async {
    // Arrange
    when(mockRepository.getItemsResult())
        .thenAnswer((_) async => Right([mockItem1, mockItem2]));

    // Act
    await controller.loadItems();

    // Assert
    expect(controller.itemsState.value.state, ViewState.content);
    expect(controller.itemsState.value.data, [mockItem1, mockItem2]);
  });

  test('should set error state when loading fails', () async {
    // Arrange
    final error = NetworkError(message: 'Network error', code: 'NETWORK_ERROR');
    when(mockRepository.getItemsResult())
        .thenAnswer((_) async => Left(error));

    // Act
    await controller.loadItems();

    // Assert
    expect(controller.itemsState.value.state, ViewState.error);
    expect(controller.itemsState.value.error, error);
  });
});
```

### **Widget Testing**

```dart
testWidgets('should display loading widget initially', (tester) async {
  await tester.pumpWidget(MyApp());
  
  expect(find.byType(CircularProgressIndicator), findsOneWidget);
});

testWidgets('should display items when loaded', (tester) async {
  controller.itemsState.value = ViewModel.content([mockItem1, mockItem2]);
  
  await tester.pumpWidget(MyApp());
  await tester.pump();
  
  expect(find.byType(MyItemWidget), findsNWidgets(2));
});
```

## Performance Considerations

### **Efficient State Updates**
- Use `Obx()` only around widgets that need to react to state changes
- Avoid wrapping entire pages in `Obx()` - be granular
- Use `const` constructors for ViewModel states when possible

### **Memory Management**
- Dispose controllers properly in bindings
- Use `Get.find()` instead of `Get.put()` for shared controllers
- Clear large data sets when navigating away from pages

## Related Documentation

- **[Error Handling Architecture](./error_handling_architecture.md)** - Comprehensive error handling patterns
- **[Result to ViewModel Patterns](./result_to_viewmodel_standard_pattern.md)** - Functional conversion patterns

This state management architecture provides a consistent, reactive foundation for building robust Flutter applications with excellent user experience and developer productivity.
