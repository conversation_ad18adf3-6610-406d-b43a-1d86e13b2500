# State Management with ViewModel

## Overview

Comprehensive state management architecture using ViewModel pattern with EntityStateWidget for consistent, reactive UI state handling across the Flutter application.

## ViewModel Architecture

### **Core ViewModel Structure**

```dart
class ViewModel<T> {
  final ViewState state;
  final T? data;
  final dynamic error;  // String or AppError
  
  const ViewModel._({
    required this.state,
    this.data,
    this.error,
  });
  
  // Factory constructors for different states
  const ViewModel.loading() : this._(state: ViewState.loading);
  const ViewModel.empty() : this._(state: ViewState.empty);
  const ViewModel.content(T data) : this.._(state: ViewState.content, data: data);
  const ViewModel.error(dynamic error) : this._(state: ViewState.error, error: error);
}

enum ViewState { loading, content, empty, error }
```

### **Dynamic Error Handling**

The ViewModel supports both legacy string errors and modern AppError objects:

```dart
// Legacy string error
ViewModel<List<Item>>.error("Network connection failed")

// Modern AppError
ViewModel<List<Item>>.error(NetworkError(
  message: "Unable to load items. Please check your connection.",
  code: "NETWORK_ERROR",
  severity: ErrorSeverity.warning,
))
```

## EntityStateWidget

### **Core Implementation**

```dart
class EntityStateWidget<T> extends StatelessWidget {
  final ViewModel<T> model;
  final Widget Function(T data) itemBuilder;
  final VoidCallback? onRetry;
  final Widget? loadingWidget;
  final Widget? emptyWidget;
  final Widget? errorWidget;

  Widget build(BuildContext context) {
    switch (model.state) {
      case ViewState.loading:
        return loadingWidget ?? DefaultLoadingWidget();
      case ViewState.content:
        return itemBuilder(model.data!);
      case ViewState.empty:
        return emptyWidget ?? DefaultEmptyWidget(onRetry: onRetry);
      case ViewState.error:
        return errorWidget ?? DefaultErrorWidget(
          error: model.error,
          onRetry: onRetry,
        );
    }
  }
}
```

### **Enhanced Error Display**

```dart
class DefaultErrorWidget extends StatelessWidget {
  final dynamic error;
  final VoidCallback? onRetry;

  Widget build(BuildContext context) {
    final isAppError = error is AppError;
    final appError = isAppError ? error as AppError : null;
    
    return Column(
      children: [
        // Error icon based on severity
        Icon(_getErrorIcon(appError?.severity)),
        
        // User-friendly message
        Text(isAppError ? appError!.message : error.toString()),
        
        // Retry button
        if (onRetry != null)
          ElevatedButton(
            onPressed: onRetry,
            child: Text('Retry'),
          ),
        
        // Debug information (development only)
        if (kDebugMode && isAppError)
          _buildDebugInfo(appError!),
      ],
    );
  }
}
```

## Controller Patterns

### **Standard Controller Structure**

```dart
class MyController extends GetxController {
  // Reactive state variables
  final Rx<ViewModel<List<MyModel>>> itemsState = 
      const ViewModel<List<MyModel>>.loading().obs;
  
  final Rx<ViewModel<MyModel>> selectedItemState = 
      const ViewModel<MyModel>.loading().obs;
  
  // Error handler
  final ErrorHandler _errorHandler = Get.find<ErrorHandler>();
  
  @override
  void onInit() {
    super.onInit();
    loadItems();
  }
}
```

### **Data Loading Patterns**

#### **List Data Loading**
```dart
Future<void> loadItems() async {
  itemsState.value = const ViewModel<List<MyModel>>.loading();
  
  final result = await repository.getItemsResult();
  
  itemsState.value = result.toListViewModel<MyModel>(
    onError: (error) => _errorHandler.displayErrorToast(error, 'loadItems'),
  );
}
```

#### **Single Item Loading**
```dart
Future<void> loadItem(String id) async {
  selectedItemState.value = const ViewModel<MyModel>.loading();
  
  final result = await repository.getItemResult(id);
  
  selectedItemState.value = result.toViewModel<MyModel>(
    onError: (error) => _errorHandler.displayErrorToast(error, 'loadItem'),
    onSuccess: (item) => _logItemLoaded(item),
  );
}
```

#### **Refresh Pattern**
```dart
Future<void> refreshItems() async {
  // Don't show loading state for refresh
  final result = await repository.getItemsResult();
  
  itemsState.value = result.toListViewModel<MyModel>(
    onError: (error) => _errorHandler.displayErrorToast(error, 'refreshItems'),
  );
}
```

## UI Integration Patterns

### **Basic Usage**

```dart
class MyPage extends StatelessWidget {
  final MyController controller = Get.find<MyController>();

  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(() => EntityStateWidget<List<MyModel>>(
        model: controller.itemsState,
        onRetry: () => controller.loadItems(),
        itemBuilder: (items) => ListView.builder(
          itemCount: items.length,
          itemBuilder: (context, index) => MyItemWidget(items[index]),
        ),
      )),
    );
  }
}
```

### **Custom State Widgets**

```dart
EntityStateWidget<List<Product>>(
  model: controller.productsState,
  onRetry: () => controller.loadProducts(),
  itemBuilder: (products) => ProductGrid(products: products),
  loadingWidget: ProductGridSkeleton(),
  emptyWidget: EmptyProductsWidget(
    onAddProduct: () => controller.navigateToAddProduct(),
  ),
  errorWidget: CustomErrorWidget(
    onRetry: () => controller.loadProducts(),
    onContactSupport: () => controller.contactSupport(),
  ),
)
```

### **Nested State Management**

```dart
class ProfilePage extends StatelessWidget {
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Profile header
        Obx(() => EntityStateWidget<ProfileModel>(
          model: controller.profileState,
          onRetry: () => controller.loadProfile(),
          itemBuilder: (profile) => ProfileHeader(profile: profile),
        )),
        
        // Posts section
        Expanded(
          child: Obx(() => EntityStateWidget<List<PostModel>>(
            model: controller.postsState,
            onRetry: () => controller.loadPosts(),
            itemBuilder: (posts) => PostsList(posts: posts),
          )),
        ),
      ],
    );
  }
}
```

## Advanced Patterns

### **Conditional State Updates**

```dart
Future<void> loadItemsIfNeeded() async {
  // Only load if not already loaded or in error state
  if (itemsState.value.state == ViewState.content) {
    return; // Already loaded
  }
  
  await loadItems();
}
```

### **Optimistic Updates**

```dart
Future<void> toggleFavorite(String itemId) async {
  // Optimistically update UI
  final currentItems = itemsState.value.data ?? [];
  final updatedItems = currentItems.map((item) {
    if (item.id == itemId) {
      return item.copyWith(isFavorite: !item.isFavorite);
    }
    return item;
  }).toList();
  
  itemsState.value = ViewModel<List<MyModel>>.content(updatedItems);
  
  // Perform actual update
  final result = await repository.toggleFavoriteResult(itemId);
  
  result.fold(
    (error) {
      // Revert on error
      itemsState.value = ViewModel<List<MyModel>>.content(currentItems);
      _errorHandler.displayErrorToast(error, 'toggleFavorite');
    },
    (updatedItem) {
      // Update with server response
      final finalItems = currentItems.map((item) {
        return item.id == itemId ? updatedItem : item;
      }).toList();
      itemsState.value = ViewModel<List<MyModel>>.content(finalItems);
    },
  );
}
```

### **Pagination Support**

```dart
class PaginatedController extends GetxController {
  final Rx<ViewModel<List<MyModel>>> itemsState = 
      const ViewModel<List<MyModel>>.loading().obs;
  
  final RxBool isLoadingMore = false.obs;
  int _currentPage = 1;
  bool _hasMoreData = true;

  Future<void> loadItems({bool isRefresh = false}) async {
    if (isRefresh) {
      _currentPage = 1;
      _hasMoreData = true;
      itemsState.value = const ViewModel<List<MyModel>>.loading();
    }
    
    final result = await repository.getItemsResult(page: _currentPage);
    
    result.fold(
      (error) {
        if (isRefresh) {
          itemsState.value = ViewModel<List<MyModel>>.error(error);
        }
        _errorHandler.displayErrorToast(error, 'loadItems');
      },
      (newItems) {
        final currentItems = isRefresh ? <MyModel>[] : (itemsState.value.data ?? <MyModel>[]);
        final allItems = [...currentItems, ...newItems];
        
        _hasMoreData = newItems.isNotEmpty;
        _currentPage++;
        
        itemsState.value = allItems.isEmpty 
            ? const ViewModel<List<MyModel>>.empty()
            : ViewModel<List<MyModel>>.content(allItems);
      },
    );
  }

  Future<void> loadMore() async {
    if (!_hasMoreData || isLoadingMore.value) return;
    
    isLoadingMore.value = true;
    await loadItems();
    isLoadingMore.value = false;
  }
}
```

## Testing Patterns

### **State Testing**

```dart
group('ViewModel State Management', () {
  test('should start with loading state', () {
    expect(controller.itemsState.value.state, ViewState.loading);
  });

  test('should set content state when data loads successfully', () async {
    // Arrange
    when(mockRepository.getItemsResult())
        .thenAnswer((_) async => Right([mockItem1, mockItem2]));

    // Act
    await controller.loadItems();

    // Assert
    expect(controller.itemsState.value.state, ViewState.content);
    expect(controller.itemsState.value.data, [mockItem1, mockItem2]);
  });

  test('should set error state when loading fails', () async {
    // Arrange
    final error = NetworkError(message: 'Network error', code: 'NETWORK_ERROR');
    when(mockRepository.getItemsResult())
        .thenAnswer((_) async => Left(error));

    // Act
    await controller.loadItems();

    // Assert
    expect(controller.itemsState.value.state, ViewState.error);
    expect(controller.itemsState.value.error, error);
  });
});
```

### **Widget Testing**

```dart
testWidgets('should display loading widget initially', (tester) async {
  await tester.pumpWidget(MyApp());
  
  expect(find.byType(CircularProgressIndicator), findsOneWidget);
});

testWidgets('should display items when loaded', (tester) async {
  controller.itemsState.value = ViewModel.content([mockItem1, mockItem2]);
  
  await tester.pumpWidget(MyApp());
  await tester.pump();
  
  expect(find.byType(MyItemWidget), findsNWidgets(2));
});
```

## Performance Considerations

### **Efficient State Updates**
- Use `Obx()` only around widgets that need to react to state changes
- Avoid wrapping entire pages in `Obx()` - be granular
- Use `const` constructors for ViewModel states when possible

### **Memory Management**
- Dispose controllers properly in bindings
- Use `Get.find()` instead of `Get.put()` for shared controllers
- Clear large data sets when navigating away from pages

## Related Documentation

- **[Error Handling Architecture](./error_handling_architecture.md)** - Comprehensive error handling patterns
- **[Result to ViewModel Patterns](./result_to_viewmodel_standard_pattern.md)** - Functional conversion patterns

This state management architecture provides a consistent, reactive foundation for building robust Flutter applications with excellent user experience and developer productivity.
